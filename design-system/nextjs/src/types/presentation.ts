export type PromptStatus = "draft" | "published" | "archived";
export type PromptVisibility = "private" | "public" | "shared";
export type TargetAI = "gpt-4" | "claude" | "gemini" | "llama" | "custom";

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  createdAt: Date;
}

export interface Tag {
  id: string;
  name: string;
  color: string;
  createdAt: Date;
}

export interface Prompt {
  id: string;
  title: string;
  content: string;
  status: PromptStatus;
  visibility: PromptVisibility;
  targetAI: TargetAI;
  authorId: string;
  author: User;
  tags: Tag[];
  isFavorite: boolean;
  createdAt: Date;
  updatedAt: Date;
  sharedWith: string[];
  shareLink?: string;
  commentCount: number;
}

export interface Comment {
  id: string;
  promptId: string;
  authorId: string;
  author: User;
  content: string;
  createdAt: Date;
  updatedAt?: Date;
}

export interface Notification {
  id: string;
  userId: string;
  type: "comment" | "share" | "mention";
  title: string;
  message: string;
  isRead: boolean;
  relatedPromptId?: string;
  relatedCommentId?: string;
  createdAt: Date;
}

export interface PromptFilters {
  status?: PromptStatus;
  targetAI?: TargetAI;
  authorId?: string;
  tags?: string[];
  searchText?: string;
  isFavorite?: boolean;
  sharedWithMe?: boolean;
}

export interface PromptViewModel {
  prompts: Prompt[];
  filteredPrompts: Prompt[];
  filters: PromptFilters;
  isLoading: boolean;
  error?: string;
}

export interface CollaborationViewModel {
  comments: Comment[];
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error?: string;
}

