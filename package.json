{"name": "clean-archi-front", "version": "1.0.0", "private": true, "type": "module", "workspaces": ["src", "apps/client/angular-app", "apps/client/nextjs-app", "apps/client/stencil-app", "eslint-plugin-custom-rules", "design-system/angular", "design-system/nextjs", "design-system/stencil", "apps/server/hono-api", "apps/server/nest-api"], "scripts": {"lint": "eslint -c eslint.config.js src", "angular-app:dev": "cd apps/client/angular-app && npm start", "nextjs-app:dev": "cd apps/client/nextjs-app && npm run dev", "nextjs-app:build": "cd apps/client/nextjs-app && npm run build", "stencil-app:dev": "cd apps/client/stencil-app && npm start", "hono-api:dev": "cd apps/server/hono-api && npm run dev", "nest-api:dev": "cd apps/server/nest-api && npm run start:dev", "test": "npm run test:src && npm run test:angular-app && npm run test:nextjs-app && npm run test:stencil-app && npm run test:hono-api && npm run test:nest-api", "test:src": "vitest run", "coverage:src": "vitest run --coverage", "test:angular-app": "npm run -w apps/client/angular-app test", "test:nextjs-app": "npm run -w apps/client/nextjs-app test", "test:stencil-app": "npm run -w apps/client/stencil-app test", "test:hono-api": "npm run -w apps/server/hono-api test", "test:nest-api": "npm run -w apps/server/nest-api test", "mutation": "stryker run", "depcruise": "depcruise --config .dependency-cruiser.cjs src", "deps:promptManagement": "depcruise --config .dependency-cruiser.cjs src --include-only \"^src/server/PromptManagement\" --exclude \"^src/server/.*/specs\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/dependencies/promptManagement.html", "deps:sharingAndVisibility": "depcruise --config .dependency-cruiser.cjs src --include-only \"^src/server/SharingAndVisibility\" --exclude \"^src/server/.*/specs\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/dependencies/sharingAndVisibility.html", "deps:shared": "depcruise --config .dependency-cruiser.cjs src --include-only \"^src/server/Shared\" --exclude \"^src/server/.*/specs\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/dependencies/shared.html", "deps:collaboration": "depcruise --config .dependency-cruiser.cjs src --include-only \"^src/server/Collaboration\" --exclude \"^src/server/.*/specs\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/dependencies/collaboration.html", "deps:authentication": "depcruise --config .dependency-cruiser.cjs src --include-only \"^src/server/Authentication\" --exclude \"^src/server/.*/specs\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/dependencies/authentication.html", "deps:src": "depcruise --config .dependency-cruiser.cjs src --include-only \"^src/server\" --exclude \"^src/server/.*/specs\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/dependencies/src.html", "deps:all": "npm run deps:promptManagement && npm run deps:sharingAndVisibility && npm run deps:shared && npm run deps:collaboration && npm run deps:authentication"}, "devDependencies": {"@stryker-mutator/core": "^7.1.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/eslint-plugin": "^1.3.3", "dependency-cruiser": "^16.10.3", "eslint": "^9.29.0", "eslint-config-next": "15.1.0", "eslint-plugin-boundaries": "^5.0.1", "eslint-plugin-clean-architecture": "^0.1.0", "eslint-plugin-custom-rules": "file:eslint-plugin-custom-rules", "eslint-plugin-jest": "^29.0.1", "msw": "^2.10.3", "typescript": "^5.8.3"}, "dependencies": {"@evyweb/ioctopus": "^1.2.0", "@tanstack/query-core": "^5.81.5", "tw-animate-css": "^1.3.7", "zod": "^3.25.76", "zustand": "^5.0.6"}}