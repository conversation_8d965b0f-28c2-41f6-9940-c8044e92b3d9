import js from '@eslint/js';
import tseslint from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import vitest from '@vitest/eslint-plugin';
import boundaries from 'eslint-plugin-boundaries';
import cleanArchitecture from 'eslint-plugin-clean-architecture';
import customRules from 'eslint-plugin-custom-rules';

export default [
  {
    files: ['**/*.ts'],
    ignores: ['node_modules'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        project: './tsconfig.base.json',
      },
      globals: {
        console: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        window: 'readonly',
        document: 'readonly',
        navigator: 'readonly',
        location: 'readonly',
      },
    },
    plugins: {
      '@typescript-eslint': tseslint,
      boundaries,
      'clean-architecture': cleanArchitecture,
      'custom-rules': customRules,
    },
    rules: {
      ...js.configs.recommended.rules,
      ...tseslint.configs.recommended.rules,
      'custom-rules/describe-naming-convention': 'error',
      'custom-rules/it-should-start-with-should': 'error',
      'custom-rules/require-arrange-act-assert-comments': 'error',
      '@typescript-eslint/parameter-properties': 'off',
      '@typescript-eslint/explicit-member-accessibility': [
        'error',
        {
          accessibility: 'no-public',
          overrides: {
            constructors: 'no-public',
          },
        },
      ],
      '@typescript-eslint/prefer-readonly': 'error',
      'boundaries/element-types': [
        'error',
        {
          default: 'allow',
          rules: [
            {
              from: 'server-domain',
              disallow: [
                'server-application',
                'server-infrastructure',
                'server-presentation',
              ],
            },
            {
              from: 'server-application',
              disallow: ['server-infrastructure', 'server-presentation'],
            },
            {
              from: 'server-infrastructure',
              disallow: ['server-domain'],
            },
            {
              from: 'server-presentation',
              disallow: ['server-domain'],
            },
            {
              from: 'client-domain',
              disallow: [
                'client-application',
                'client-infrastructure',
                'client-presentation',
              ],
            },
            {
              from: 'client-application',
              disallow: ['client-infrastructure', 'client-presentation'],
            },
              {
              from: 'client-infrastructure',
              disallow: ['client-domain'],
            },
            {
              from: 'client-presentation',
              disallow: ['client-domain'],
            },
          ],
        },
      ],
      'boundaries/external': [
        'error',
        {
          default: 'allow',
          rules: [
            {
              from: 'server-domain',
              disallow: ['*'],
            },
          ],
        },
      ],
    },
    settings: {
      'boundaries/elements': [
        { type: 'server-domain', pattern: 'server/**/domain/**' },
        { type: 'server-application', pattern: 'server/**/application/**' },
        { type: 'server-infrastructure', pattern: 'server/**/infrastructure/**' },
        { type: 'server-presentation', pattern: 'server/**/presentation/**' },
        { type: 'client-domain', pattern: 'client/**/domain/**' },
        { type: 'client-application', pattern: 'client/**/application/**' },
        { type: 'client-infrastructure', pattern: 'client/**/infrastructure/**' },
        { type: 'client-presentation', pattern: 'client/**/presentation/**' },
        { type: 'specs-layer', pattern: '**/*/specs/**' },
      ],
    },
  },
  {
    files: ['**/*.spec.ts'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        project: './tsconfig.base.json',
      },
      globals: vitest.environments.env.globals,
    },
    plugins: {
      vitest,
      boundaries,
      'custom-rules': customRules,
    },
    rules: {
      ...vitest.configs.recommended.rules,
      'vitest/max-nested-describe': ['error', { max: 3 }],
      'vitest/valid-title': 'off',
      'vitest/no-conditional-expect': 'error',
      'vitest/no-conditional-in-test': 'error',
      'vitest/no-conditional-tests': 'error',
      'vitest/no-disabled-tests': 'error',
      'vitest/no-focused-tests': 'error',
      'boundaries/element-types': [
        'error',
        {
          default: 'allow',
          rules: [
            {
              from: 'server-domain',
              disallow: [
                'server-application',
                'server-infrastructure',
                'server-presentation',
                'specs-layer',
              ],
            },
            {
              from: 'server-application',
              disallow: ['server-infrastructure', 'server-presentation', 'specs-layer'],
            },
            {
              from: 'server-infrastructure',
              disallow: ['server-domain', 'specs-layer'],
            },
            {
              from: 'server-presentation',
              disallow: ['server-domain', 'specs-layer'],
            },
            {
              from: 'client-domain',
              disallow: [
                'client-application',
                'client-infrastructure',
                'client-presentation',
                'specs-layer',
              ],
            },
            {
              from: 'client-application',
              disallow: ['client-infrastructure', 'client-presentation', 'specs-layer'],
            },
              {
              from: 'client-infrastructure',
              disallow: ['client-domain', 'specs-layer'],
            },
            {
              from: 'client-presentation',
              disallow: ['client-domain', 'specs-layer'],
            },
            {
              from: 'specs-layer',
              disallow: [],
            },
          ],
        },
      ],
      'boundaries/external': [
        'error',
        {
          default: 'allow',
          rules: [
            {
              from: 'server-domain',
              disallow: ['*'],
            },
          ],
        },
      ],
    },
    settings: {
      'boundaries/elements': [
        { type: 'server-domain', pattern: 'server/**/domain/**' },
        { type: 'server-application', pattern: 'server/**/application/**' },
        { type: 'server-infrastructure', pattern: 'server/**/infrastructure/**' },
        { type: 'server-presentation', pattern: 'server/**/presentation/**' },
        { type: 'client-domain', pattern: 'client/**/domain/**' },
        { type: 'client-application', pattern: 'client/**/application/**' },
        { type: 'client-infrastructure', pattern: 'client/**/infrastructure/**' },
        { type: 'client-presentation', pattern: 'client/**/presentation/**' },
        { type: 'specs-layer', pattern: '**/*/specs/**' },
      ],
    },
  },
];
