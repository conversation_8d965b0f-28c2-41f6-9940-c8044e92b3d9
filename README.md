# Clean Architecture Front

## Server use cases

The `src/server` directory contains several bounded contexts, each implementing specific business actions.

### PromptManagement
- **AddPromptToFavorites**: mark a user's own prompt as favorite when it is not already.
- **AddTagToPrompt**: attach a new tag to a prompt if the user owns the prompt and the tag is not yet present.
- **ArchivePrompt**: archive a prompt so it no longer appears among active prompts.
- **ChangePromptTargetAI**: change the target AI for a prompt owned by the user.
- **CreatePromptDraft**: create a new draft prompt with provided text and target AI.
- **DeletePrompt**: remove a prompt permanently from storage.
- **DuplicatePrompt**: copy an existing prompt to a new one with a new identifier.
- **EditPromptDraft**: update the text of a draft prompt.
- **PublishPrompt**: publish a prompt so it becomes available to others.
- **RemovePromptFromFavorites**: take a prompt out of the user's favorites list.
- **RemoveTagFromPrompt**: delete a tag from a prompt owned by the user.
- **RestorePrompt**: unarchive a previously archived prompt.

### Collaboration
- **AddCommentToPrompt**: create a comment on a prompt and notify listeners.
- **DeleteComment**: remove a user comment from a prompt.
- **EditComment**: modify an existing comment authored by the user.
- **MarkNotificationAsRead**: mark a notification as read for the user.
- **NotifyNewCommentOnFollowedPrompt**: inform a user when a followed prompt receives a comment.
- **NotifyPromptSharedWithMe**: alert a user when a prompt is shared with them.
- **ReceivePromptCommentsInRealtime**: stream new comments on a prompt in real time.

### Discovery
- **FilterPromptsByAuthor**: list prompts created by a given author.
- **FilterPromptsByAuthorOrShared**: display prompts from an author or those shared with a user.
- **FilterPromptsByTargetAI**: show prompts intended for a specific AI model.
- **FilterPromptsByTag**: find prompts labelled with a particular tag.
- **ListFavoritePrompts**: present prompts a user has marked as favorite.
- **ListPromptsByStatus**: list prompts by their publication status.
- **SearchPromptsByText**: search prompts containing a text fragment.

### SharingAndVisibility
- **RemoveUserAccessFromPrompt**: revoke a user's right to view a prompt.
- **SetPromptVisibility**: define whether a prompt is private, public or by link.
- **SharePromptByLink**: generate a shareable link granting access to a prompt.
- **SharePromptWithUsers**: grant selected users the right to view a prompt.
- **ViewPromptAccessList**: display who can see a prompt and how.
