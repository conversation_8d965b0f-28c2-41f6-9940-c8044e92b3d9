# ALWAYS FOLLOW THESE RULES

## General Workflow

- Follow a **Test-Driven Development** approach.
- After each iteration or change, run `npm run lint` and `npm test:src`.
- Both commands must succeed before committing.
- Keep commits focused and describe the functional change.

## Testing Guidelines

- Tests use **Vitest**.
- Use `npm test:src` to run them.
- Structure tests with the comment sections:
  ```ts
  // Arrange
  // <your code>
  
  // Act
  // <your code>
  
  // Assert
  // <your code>
  ```
- Arrange is optional if no arrangement is needed
- Only perform **one** action in the `// Act` section.
- Use **sociable tests** that exercise use cases.
- These act as acceptance tests; the domain is never tested directly.
- **Avoid overly technical tests and solitary unit tests**.
- `describe` blocks should use `describe("When ...")` to give context.
- Each `it` statement must start with `should` and stay close to the functional behaviour.
- Never leave `.only` or `.skip` in committed tests.

## Project Architecture

- The project follows **Clean Architecture + DDD + TDD** organized by **Bounded Contexts**:
    - **Structure**: `src/client/[BoundedContext]` and `src/server/[BoundedContext]`
    - **Bounded Contexts**: `Authentication`, `Collaboration`, `PromptManagement`, `SharingAndVisibility`, `Discovery`,
      maybe more...
    - **Layers**: `domain`, `application`, `infrastructure`, `presentation`, `specs` for both client and server
    - **ESLint boundary rules** enforce these constraints, you need to respect them

## Stucture

apps/
├── client/
│ ├── angular-app/
│ ├── nextjs-app/
│ └── stencil-app/
├── server/
│ ├── hono-api/
│ └── nest-api/
├── src/
│ └── client/
│ ├── Authentication/
│ ├── Collaboration/
│ ├── PromptManagement/
│ ├── SharingAndVisibility/
│ └── Discovery/
│ └── server/
│ ├── Authentication/
│ ├── Collaboration/
│ ├── PromptManagement/
│ ├── SharingAndVisibility/
│ └── Discovery/
├── design-system/
│ ├── angular/
│ ├── nextjs/
│ └── stencil/
├── vitest.config.ts
├── tsconfig.json
└── package.json

## Key Patterns for both client and server

- **UseCases**: Actions with dedicated execute method
- **Repository Pattern**: Interfaces in application, implementations in infrastructure
- **Presenter Pattern**: For view models and display logic
- **Domain Entities**: With factory methods, snapshots, and business logic
- **Value Objects**: Immutable objects representing domain concepts

## Examples

```ts
// src/server/PromptManagement/application/usecase/PublishPrompt/PublishPromptRequest.ts
type PublishPromptRequest = {
  promptId: string;
};

// src/server/PromptManagement/application/usecase/PublishPrompt/PublishPromptResponse.ts
type PublishPromptResponse = {
  promptId: string;
  status: string;
};

// src/server/PromptManagement/application/usecase/PublishPrompt/PublishPrompt.ts
export class PublishPrompt {
  private readonly promptRepository: PromptRepository;

  constructor(promptRepository: PromptRepository) {
    this.promptRepository = promptRepository;
  }

  async execute({promptId}: PublishPromptRequest, presenter: PublishPromptPresenter): Promise<void> {
    // 1. Load the prompt
    const prompt = await this.promptRepository.findById(request.promptId)
    if (!prompt) {
      presenter.displayPublicationError(new PromptNotFoundError(request.promptId));
      return;
    }

    // 2. Apply simple typescript business logic inside the domain (ex: update properties)
    prompt.publish();

    // 3. Persist the new state
    await this.promptRepository.save(prompt);

    // 4. Present the dedicated response as litteral objects which will be transformed to a view model by the presenter
    presenter.displayPublicationSuccess({
      promptId: prompt.id,
      status: prompt.status,
    });
  }
}

// src/server/PromptManagement/application/usecase/PublishPrompt/PromptNotFoundError.ts
export class PromptNotFoundError extends Error {
  constructor(promptId: string) {
    super(`Prompt not found: ${promptId}`);
  }
}

// src/server/PromptManagement/domain/Prompt/errors/PublishPromptError.ts
export type PublishPromptError = PromptNotFoundError | OtherError;

// src/server/PromptManagement/presentation/presenter/PublishPromptPresenter.ts
export class PublishPromptPresenter {
  private view: PublishPromptViewModel = {
    error: null,
    promptId: '',
    status: '',
  };

  displayPublicationSuccess(response: PublishPromptResponse): void {
    this.view = {
      error: null,
      promptId: response.promptId,
      status: response.status,
    };
  }

  displayPublicationError(error: PublishPromptError): void {
    this.view = {
      error: error.message,
      promptId: '',
      status: '',
    };
  }
}

// src/server/PromptManagement/presentation/viewModel/PublishPromptViewModel.ts
export type PublishPromptViewModel = {
  error: string | null;
  promptId: string;
  status: string;
};

// src/server/PromptManagement/application/port/PublishPromptPresenter.ts
export interface PublishPromptPresenter {
  displayPublicationSuccess(response: PublishPromptResponse): void;

  displayPublicationError(error: Error): void;
}

// src/server/PromptManagement/application/port/PromptRepository.ts
export interface PromptRepository {
  findById(id: string): Promise<Prompt | null>;

  save(prompt: Prompt): Promise<void>;
}

// src/server/PromptManagement/domain/Prompt/PromptSnapshot.ts
export type PromptSnapshot = {
  id: string;
  title: string;
  content: string;
  status: string;
}

// src/server/PromptManagement/domain/Prompt/Prompt.ts
export class Prompt {
  private readonly id: string;
  private readonly title: string;
  private readonly content: string;
  private readonly status: string;

  private constructor({id, title, content, status}: PromptSnapshot) {
    this.id = id;
    this.title = title;
    this.content = content;
    this.status = status;
  }

  static fromSnapshot(snapshot: PromptSnapshot): Prompt {
    // Validation rules here
    return new Prompt(snapshot);
  }

  toSnapshot(): PromptSnapshot {
    return {
      id: this.id,
      title: this.title,
      content: this.content,
      status: this.status,
    };
  }

  publish(): void {
    this.status = 'published';
  }
}

// src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository.ts
export class InMemoryPromptRepository implements PromptRepository {
  private prompts: Prompt[] = [];

  async findById(id: string): Promise<Prompt | null> {
    return this.prompts.find((prompt) => prompt.id === id) ?? null;
  }

  async save(prompt: Prompt): Promise<void> {
    const index = this.prompts.findIndex((p) => p.id === prompt.id);
    if (index === -1) {
      this.prompts.push(prompt);
    } else {
      this.prompts[index] = prompt;
    }
  }
}

// Examples of repositories implementations
// src/server/PromptManagement/infrastructure/PromptRepository/ConvexPromptRepository.ts
export class ConvexPromptRepository implements PromptRepository {
  // Implementation using Convex API
}

// src/server/PromptManagement/infrastructure/PromptRepository/FirebasePromptRepository.ts
export class FirebasePromptRepository implements PromptRepository {
  // Implementation using Firebase API
}

// value objects are Server only and should not be created if no logic is needed, prefer using primitives if no business logic is needed
```

## File Naming Conventions

- **UseCases**: `[Action].ts`
- **Requests**: `[Action]Request.ts`
- **Responses**: `[Action]Response.ts`
- **Repositories**: `[Entity]Repository.ts` (interface), `Convex[Entity]Repository.ts`, `InMemory[Entity]Repository.ts`
- **Presenters**: `[Action]Presenter.ts` (interface), `[Action]WebPresenter.ts`
- **Tests**: `[Action].spec.ts`
- **Domain**: `[Entity].ts`, `[ValueObject].ts`, `[Entity]Snapshot.ts`

## Id generation

- Always use `IdentityProvider` to generate ids
- `IdentityProvider` is implemented by `UuidIdentityProvider` and `FakeIdentityProvider`
- `UuidIdentityProvider` uses `CryptoPort` to generate ids
- `FakeIdentityProvider` is used for testing and allows to set the next ids to be generated
- ALWAYS use UUID v4 format for all tests - no exception allowed (ex: never use p1, p2, etc..., use
  550e8400-e29b-41d4-a716-************, 550e8400-e29b-41d4-a716-************, etc...)

```ts
// src/server/Shared/application/port/IdentityProvider.ts
export interface IdentityProvider {
  generateId(): string;
}

// src/server/Shared/application/port/CryptoPort.ts
export interface CryptoPort {
  randomUUID(): string;
}

// src/server/Shared/infrastructure/IdentityProvider/UuidIdentityProvider.ts
export class UuidIdentityProvider implements IdentityProvider {
  private readonly crypto: CryptoPort;

  constructor(crypto: CryptoPort) {
    this.crypto = crypto;
  }

  generateId(): string {
    return this.crypto.randomUUID();
  }
}

// src/server/Shared/infrastructure/IdentityProvider/FakeIdentityProvider.ts
export class FakeIdentityProvider implements IdentityProvider {
  private ids: [] = []; // Always use UUID v4 format for all tests - no exception allowed

  generateId(): string {
    return this.ids.shift()!;
  }

  setNextIds(...nextIds: string[]): void {
    this.ids = nextIds;
  }
}
```

## Code Style

- Always use dedicated fields for class properties instead of constructor parameters syntax

```ts
// Bad
export class XXX {
  constructor(
    private readonly a: string,
    private readonly b: string,
    private readonly d: string,
  ) {
  }

  // ...
}

// Good
export class XXX {
  private readonly a: string;
  private readonly b: string;
  private readonly c: string;

  constructor({a, b, c}: XXXSnapshot) {
    this.a = a;
    this.b = b;
    this.c = c;
  }

  // ...
}
```

## Helpers and Test classes

- Helpers and test classes are not allowed outside of the specs folder (ex: fakes, helpers, testing classes, etc...)
- They should be extracted from the specs to their own dedicated file in specs/{helpers,fakes,...} folder

```
// Example:
class TestPresenter {
  response: RestorePromptResponse | null = null;
  displayPromptRestored(response: RestorePromptResponse): void {
    this.response = response;
  }
}
```

## Import Conventions

- Path alias `@/*` points to the repository root
- Always use absolute imports with the `@/` alias
- Import from interfaces, not implementations
- Follow dependency direction: domain ← application ← infrastructure/presentation

## Tools

- **Linting**: `npm run lint` (TypeScript + ESLint with boundary rules)
- **Testing**: `npm test` (Vitest)

## Frontend Specific

- **Framework**: Angular 20,Next.js 15 with React 19, Stencil 4
- **Styling**: Tailwind CSS

## Backend Specific

- **Framework**: NestJS 10, Hono 3
- **Testing**: Vitest 6

## Commit Requirements

- Keep the working directory clean before committing
- Include relevant tests for new features or fixes
- Ensure `npm run lint` and `npm test:src` pass before every commit
- Follow conventional commit messages describing functional changes
- Branch names should be descriptive and related to the task or feature in english
- Branch names must be in English and describe the task or feature
- Pull requests title must follow conventional commit messages

## Testing conventions
- Test intention must be only focus on functionalities, not implementation details!
- Use a dedicated `describe` block to express context (e.g. `describe("When user...")`)
- Keep `it` statements simple and starting with `should`
- Important: never `describe` test context inside the `it`! Always use `describe` for context
- Do not repeat the context inside `it` descriptions