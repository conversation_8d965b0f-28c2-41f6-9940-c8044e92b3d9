import {PromptSnapshot} from '@src/server/PromptManagement/domain/Prompt/PromptSnapshot';

export class Prompt {
  private readonly id: string;
  private readonly ownerId: string;
  private content: string;
  private targetAI: string;
  private tags: string[];
  private favorite: boolean;
  private archived: boolean;
  private status: 'draft' | 'published';
  private isTemplate: boolean;

  private constructor(snapshot: PromptSnapshot) {
    this.id = snapshot.id;
    this.ownerId = snapshot.ownerId;
    this.content = snapshot.content;
    this.targetAI = snapshot.targetAI;
    this.tags = [...snapshot.tags];
    this.favorite = snapshot.favorite;
    this.archived = snapshot.archived;
    this.status = snapshot.status;
    this.isTemplate = snapshot.isTemplate;
  }

  static createDraft(id: string, content: string, targetAI: string, ownerId: string): Prompt {
    return new Prompt({
      id,
      ownerId,
      content,
      targetAI,
      tags: [],
      favorite: false,
      archived: false,
      status: 'draft',
      isTemplate: false,
    });
  }

  static fromSnapshot(snapshot: PromptSnapshot): Prompt {
    return new Prompt(snapshot);
  }

  toSnapshot(): PromptSnapshot {
    return {
      id: this.id,
      content: this.content,
      targetAI: this.targetAI,
      tags: [...this.tags],
      favorite: this.favorite,
      archived: this.archived,
      status: this.status,
      ownerId: this.ownerId,
      isTemplate: this.isTemplate,
    };
  }

  getId(): string {
    return this.id;
  }

  hasOwner(userId: string): boolean {
    return this.ownerId === userId;
  }

  hasNotOwner(userId: string): boolean {
    return !this.hasOwner(userId);
  }

  hasTag(tag: string): boolean {
    return this.tags.includes(tag);
  }

  hasNotTag(tag: string): boolean {
    return !this.hasTag(tag);
  }

  addToFavorites(): void {
    this.favorite = true;
  }

  removeFromFavorites(): void {
    this.favorite = false;
  }

  addTag(tag: string): void {
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
  }

  removeTag(tag: string): void {
    this.tags = this.tags.filter((t) => t !== tag);
  }

  archive(): void {
    this.archived = true;
  }

  restore(): void {
    this.archived = false;
  }

  changeTargetAI(targetAI: string): void {
    this.targetAI = targetAI;
  }

  editDraft(content: string): void {
    if (this.status === 'draft') {
      this.content = content;
    }
  }

  publish(): void {
    if (this.status === 'draft') {
      this.status = 'published';
    }
  }

  duplicate(newId: string): Prompt {
    return Prompt.fromSnapshot({
      ...this.toSnapshot(),
      id: newId,
      favorite: false,
      archived: false,
    });
  }

  isInFavorite() {
    return this.favorite;
  }

  isArchived() {
    return this.archived;
  }

  isPublished() {
    return this.status === 'published';
  }

  isNotInFavorite() {
    return !this.isInFavorite();
  }

  isNotArchived() {
    return !this.isArchived();
  }

  toHaveTag(tag: string): boolean {
    return this.tags.includes(tag);
  }

  markAsTemplate(): void {
    this.isTemplate = true;
  }

  unmarkAsTemplate(): void {
    this.isTemplate = false;
  }

  isATemplate(): boolean {
    return this.isTemplate;
  }
}
