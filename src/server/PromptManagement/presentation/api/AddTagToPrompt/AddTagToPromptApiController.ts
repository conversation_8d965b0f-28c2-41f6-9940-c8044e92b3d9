import { AddTagToPromptUseCase } from "@src/server/PromptManagement/application/port/driving/AddTagToPromptUseCase";
import { AddTagToPromptApiPresenter } from '@src/server/PromptManagement/presentation/api/AddTagToPrompt/AddTagToPromptApiPresenter';
import { AddTagToPromptApiViewModel } from '@src/server/PromptManagement/presentation/api/AddTagToPrompt/AddTagToPromptApiViewModel';
import { AuthenticationGateway } from '@src/server/Authentication';
import {AddTagToPromptApiRequest} from './AddTagToPromptApiRequest';

export class AddTagToPromptApiController {
  private readonly usecase: AddTagToPromptUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: AddTagToPromptUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: AddTagToPromptApiRequest): Promise<AddTagToPromptApiViewModel> {
    const presenter = new AddTagToPromptApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
