import {AddTagToPromptPresenter} from '@src/server/PromptManagement/application/port/driven/AddTagToPromptPresenter';
import {
  AddTagToPromptResponse
} from '@src/server/PromptManagement/application/usecase/AddTagToPrompt/AddTagToPromptResponse';
import {AddTagToPromptApiViewModel} from '@src/server/PromptManagement/presentation/api/AddTagToPrompt/AddTagToPromptApiViewModel';

export class AddTagToPromptApiPresenter implements AddTagToPromptPresenter {
  private viewModel: AddTagToPromptApiViewModel | null = null;

  getViewModel(): AddTagToPromptApiViewModel {
    return this.viewModel!;
  }

  presentTagAddedSuccessfully(response: AddTagToPromptResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId, tag: response.tag}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyTagAlreadyExists(promptId: string, tag: string): void {
    this.viewModel = {status: 409, body: {error: `Tag already exists for prompt ${promptId}: ${tag}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
