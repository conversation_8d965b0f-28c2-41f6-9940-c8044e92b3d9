import {
  AddPromptToFavoritesPresenter
} from '@src/server/PromptManagement/application/port/driven/AddPromptToFavoritesPresenter';
import {
  AddPromptToFavoritesResponse
} from '@src/server/PromptManagement/application/usecase/AddPromptToFavorites/AddPromptToFavoritesResponse';
import {
  AddPromptToFavoritesApiViewModel
} from "@src/server/PromptManagement/presentation/api/AddPromptToFavorites/AddPromptToFavoritesApiViewModel";

export class AddPromptToFavoritesApiPresenter implements AddPromptToFavoritesPresenter {
  private viewModel: AddPromptToFavoritesApiViewModel | null = null;

  getViewModel(): AddPromptToFavoritesApiViewModel {
    return this.viewModel!;
  }

  presentPromptSuccessfullyAddedToFavorites(response: AddPromptToFavoritesResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyPromptAlreadyInFavorite(promptId: string): void {
    this.viewModel = {status: 409, body: {error: `Prompt already in favorites: ${promptId}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
