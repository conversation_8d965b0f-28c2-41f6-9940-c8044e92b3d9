import {
  AddPromptToFavoritesApiPresenter
} from '@src/server/PromptManagement/presentation/api/AddPromptToFavorites/AddPromptToFavoritesApiPresenter';
import {
  AddPromptToFavoritesApiViewModel
} from "@src/server/PromptManagement/presentation/api/AddPromptToFavorites/AddPromptToFavoritesApiViewModel";
import {
  AddPromptToFavoritesUseCase
} from "@src/server/PromptManagement/application/port/driving/AddPromptToFavoritesUseCase";
import {AuthenticationGateway} from '@src/server/Authentication';
import {AddPromptToFavoritesApiRequest} from './AddPromptToFavoritesApiRequest';

export class AddPromptToFavoritesApiController {
  private readonly usecase: AddPromptToFavoritesUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: AddPromptToFavoritesUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: AddPromptToFavoritesApiRequest): Promise<AddPromptToFavoritesApiViewModel> {
    const presenter = new AddPromptToFavoritesApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
