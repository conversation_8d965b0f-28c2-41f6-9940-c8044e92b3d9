import {PublishPromptPresenter} from '@src/server/PromptManagement/application/port/driven/PublishPromptPresenter';
import {
  PublishPromptResponse
} from '@src/server/PromptManagement/application/usecase/PublishPrompt/PublishPromptResponse';
import {PublishPromptApiViewModel} from '@src/server/PromptManagement/presentation/api/PublishPrompt/PublishPromptApiViewModel';

export class PublishPromptApiPresenter implements PublishPromptPresenter {
  private viewModel: PublishPromptApiViewModel | null = null;

  getViewModel(): PublishPromptApiViewModel {
    return this.viewModel!;
  }

  displayPromptPublished(response: PublishPromptResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId, status: response.status}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyPromptAlreadyPublished(promptId: string): void {
    this.viewModel = {status: 409, body: {error: `Prompt already published: ${promptId}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
