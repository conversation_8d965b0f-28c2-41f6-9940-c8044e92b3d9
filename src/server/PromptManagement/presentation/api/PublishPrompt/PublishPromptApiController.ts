import {PublishPromptUseCase} from "@src/server/PromptManagement/application/port/driving/PublishPromptUseCase";
import {PublishPromptApiPresenter} from '@src/server/PromptManagement/presentation/api/PublishPrompt/PublishPromptApiPresenter';
import {PublishPromptApiViewModel} from '@src/server/PromptManagement/presentation/api/PublishPrompt/PublishPromptApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {PublishPromptApiRequest} from './PublishPromptApiRequest';

export class PublishPromptApiController {
  private readonly usecase: PublishPromptUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: PublishPromptUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: PublishPromptApiRequest): Promise<PublishPromptApiViewModel> {
    const presenter = new PublishPromptApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
