import { DeletePromptUseCase } from "@src/server/PromptManagement/application/port/driving/DeletePromptUseCase";
import { DeletePromptApiPresenter } from '@src/server/PromptManagement/presentation/api/DeletePrompt/DeletePromptApiPresenter';
import { DeletePromptApiViewModel } from '@src/server/PromptManagement/presentation/api/DeletePrompt/DeletePromptApiViewModel';
import { AuthenticationGateway } from '@src/server/Authentication';
import {DeletePromptApiRequest} from './DeletePromptApiRequest';

export class DeletePromptApiController {
  private readonly usecase: DeletePromptUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: DeletePromptUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: DeletePromptApiRequest): Promise<DeletePromptApiViewModel> {
    const presenter = new DeletePromptApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
