import {DeletePromptPresenter} from '@src/server/PromptManagement/application/port/driven/DeletePromptPresenter';
import {DeletePromptResponse} from '@src/server/PromptManagement/application/usecase/DeletePrompt/DeletePromptResponse';
import {DeletePromptApiViewModel} from '@src/server/PromptManagement/presentation/api/DeletePrompt/DeletePromptApiViewModel';

export class DeletePromptApiPresenter implements DeletePromptPresenter {
  private viewModel: DeletePromptApiViewModel | null = null;

  getViewModel(): DeletePromptApiViewModel {
    return this.viewModel!;
  }

  displayPromptDeleted(response: DeletePromptResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
