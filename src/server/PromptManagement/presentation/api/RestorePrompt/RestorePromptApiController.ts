import {RestorePromptUseCase} from "@src/server/PromptManagement/application/port/driving/RestorePromptUseCase";
import {RestorePromptApiPresenter} from '@src/server/PromptManagement/presentation/api/RestorePrompt/RestorePromptApiPresenter';
import {RestorePromptApiViewModel} from '@src/server/PromptManagement/presentation/api/RestorePrompt/RestorePromptApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {RestorePromptApiRequest} from './RestorePromptApiRequest';

export class RestorePromptApiController {
  private readonly usecase: RestorePromptUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: RestorePromptUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: RestorePromptApiRequest): Promise<RestorePromptApiViewModel> {
    const presenter = new RestorePromptApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
