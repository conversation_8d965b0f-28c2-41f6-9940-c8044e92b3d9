import {RestorePromptPresenter} from '@src/server/PromptManagement/application/port/driven/RestorePromptPresenter';
import {
  RestorePromptResponse
} from '@src/server/PromptManagement/application/usecase/RestorePrompt/RestorePromptResponse';
import {RestorePromptApiViewModel} from '@src/server/PromptManagement/presentation/api/RestorePrompt/RestorePromptApiViewModel';

export class RestorePromptApiPresenter implements RestorePromptPresenter {
  private viewModel: RestorePromptApiViewModel | null = null;

  getViewModel(): RestorePromptApiViewModel {
    return this.viewModel!;
  }

  presentPromptSuccessfullyRestored(response: RestorePromptResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyPromptNotArchived(promptId: string): void {
    this.viewModel = {status: 409, body: {error: `Prompt not archived: ${promptId}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
