import {
  RemovePromptFromFavoritesUseCase
} from "@src/server/PromptManagement/application/port/driving/RemovePromptFromFavoritesUseCase";
import {
  RemovePromptFromFavoritesApiPresenter
} from '@src/server/PromptManagement/presentation/api/RemovePromptFromFavorites/RemovePromptFromFavoritesApiPresenter';
import {
  RemovePromptFromFavoritesApiViewModel
} from '@src/server/PromptManagement/presentation/api/RemovePromptFromFavorites/RemovePromptFromFavoritesApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {RemovePromptFromFavoritesApiRequest} from './RemovePromptFromFavoritesApiRequest';

export class RemovePromptFromFavoritesApiController {
  private readonly usecase: RemovePromptFromFavoritesUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: RemovePromptFromFavoritesUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: RemovePromptFromFavoritesApiRequest): Promise<RemovePromptFromFavoritesApiViewModel> {
    const presenter = new RemovePromptFromFavoritesApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
