import {
  RemovePromptFromFavoritesPresenter
} from '@src/server/PromptManagement/application/port/driven/RemovePromptFromFavoritesPresenter';
import {
  RemovePromptFromFavoritesResponse
} from '@src/server/PromptManagement/application/usecase/RemovePromptFromFavorites/RemovePromptFromFavoritesResponse';
import {
  RemovePromptFromFavoritesApiViewModel
} from '@src/server/PromptManagement/presentation/api/RemovePromptFromFavorites/RemovePromptFromFavoritesApiViewModel';

export class RemovePromptFromFavoritesApiPresenter implements RemovePromptFromFavoritesPresenter {
  private viewModel: RemovePromptFromFavoritesApiViewModel | null = null;

  getViewModel(): RemovePromptFromFavoritesApiViewModel {
    return this.viewModel!;
  }

  presentPromptRemovedFromFavorites(response: RemovePromptFromFavoritesResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId, isFavorite: response.isFavorite}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyPromptNotInFavorite(promptId: string): void {
    this.viewModel = {status: 409, body: {error: `Prompt not in favorites: ${promptId}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
