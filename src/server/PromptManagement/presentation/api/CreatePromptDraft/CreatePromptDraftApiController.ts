import { CreatePromptDraftUseCase } from "@src/server/PromptManagement/application/port/driving/CreatePromptDraftUseCase";
import { CreatePromptDraftApiPresenter } from '@src/server/PromptManagement/presentation/api/CreatePromptDraft/CreatePromptDraftApiPresenter';
import { CreatePromptDraftApiViewModel } from '@src/server/PromptManagement/presentation/api/CreatePromptDraft/CreatePromptDraftApiViewModel';
import { AuthenticationGateway } from '@src/server/Authentication';
import {CreatePromptDraftApiRequest} from './CreatePromptDraftApiRequest';

export class CreatePromptDraftApiController {
  private readonly usecase: CreatePromptDraftUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: CreatePromptDraftUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: CreatePromptDraftApiRequest): Promise<CreatePromptDraftApiViewModel> {
    const presenter = new CreatePromptDraftApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, authorId: userId }, presenter);
    return presenter.getViewModel();
  }
}
