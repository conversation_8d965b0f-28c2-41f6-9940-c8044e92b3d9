import {
  CreatePromptDraftPresenter
} from '@src/server/PromptManagement/application/port/driven/CreatePromptDraftPresenter';
import {
  CreatePromptDraftResponse
} from '@src/server/PromptManagement/application/usecase/CreatePromptDraft/CreatePromptDraftResponse';
import {
  CreatePromptDraftApiViewModel
} from '@src/server/PromptManagement/presentation/api/CreatePromptDraft/CreatePromptDraftApiViewModel';

export class CreatePromptDraftApiPresenter implements CreatePromptDraftPresenter {
  private viewModel: CreatePromptDraftApiViewModel | null = null;

  getViewModel(): CreatePromptDraftApiViewModel {
    return this.viewModel!;
  }

  presentPromptDraftSuccessfullyCreated(response: CreatePromptDraftResponse): void {
    this.viewModel = {status: 201, body: {promptId: response.promptId}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
