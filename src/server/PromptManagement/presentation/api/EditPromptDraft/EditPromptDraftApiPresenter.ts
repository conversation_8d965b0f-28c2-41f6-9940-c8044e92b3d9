import {EditPromptDraftPresenter} from '@src/server/PromptManagement/application/port/driven/EditPromptDraftPresenter';
import {
  EditPromptDraftResponse
} from '@src/server/PromptManagement/application/usecase/EditPromptDraft/EditPromptDraftResponse';
import {EditPromptDraftApiViewModel} from '@src/server/PromptManagement/presentation/api/EditPromptDraft/EditPromptDraftApiViewModel';

export class EditPromptDraftApiPresenter implements EditPromptDraftPresenter {
  private viewModel: EditPromptDraftApiViewModel | null = null;

  getViewModel(): EditPromptDraftApiViewModel {
    return this.viewModel!;
  }

  presentPromptDraftEditedSuccessfully(response: EditPromptDraftResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId, content: response.content}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
