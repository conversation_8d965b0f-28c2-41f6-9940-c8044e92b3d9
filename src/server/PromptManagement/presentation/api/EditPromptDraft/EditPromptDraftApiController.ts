import {EditPromptDraftUseCase} from "@src/server/PromptManagement/application/port/driving/EditPromptDraftUseCase";
import {
  EditPromptDraftApiPresenter
} from '@src/server/PromptManagement/presentation/api/EditPromptDraft/EditPromptDraftApiPresenter';
import {EditPromptDraftApiViewModel} from '@src/server/PromptManagement/presentation/api/EditPromptDraft/EditPromptDraftApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {EditPromptDraftApiRequest} from './EditPromptDraftApiRequest';

export class EditPromptDraftApiController {
  private readonly usecase: EditPromptDraftUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: EditPromptDraftUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: EditPromptDraftApiRequest): Promise<EditPromptDraftApiViewModel> {
    const presenter = new EditPromptDraftApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
