import { ChangePromptTargetAIUseCase } from "@src/server/PromptManagement/application/port/driving/ChangePromptTargetAIUseCase";
import { ChangePromptTargetAIApiPresenter } from '@src/server/PromptManagement/presentation/api/ChangePromptTarget/ChangePromptTargetAIApiPresenter';
import { ChangePromptTargetAIApiViewModel } from '@src/server/PromptManagement/presentation/api/ChangePromptTarget/ChangePromptTargetAIApiViewModel';
import { AuthenticationGateway } from '@src/server/Authentication';
import {ChangePromptTargetAIApiRequest} from './ChangePromptTargetAIApiRequest';

export class ChangePromptTargetAIApiController {
  private readonly usecase: ChangePromptTargetAIUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: ChangePromptTargetAIUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: ChangePromptTargetAIApiRequest): Promise<ChangePromptTargetAIApiViewModel> {
    const presenter = new ChangePromptTargetAIApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
