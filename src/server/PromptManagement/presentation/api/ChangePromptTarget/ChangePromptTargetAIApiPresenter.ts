import {
  ChangePromptTargetAIPresenter
} from '@src/server/PromptManagement/application/port/driven/ChangePromptTargetAIPresenter';
import {
  ChangePromptTargetAIResponse
} from '@src/server/PromptManagement/application/usecase/ChangePromptTargetAI/ChangePromptTargetAIResponse';
import {PromptNotFoundError} from '@src/server/PromptManagement/domain/Prompt/errors/PromptNotFoundError';
import {NotPromptOwnerError} from '@src/server/PromptManagement/domain/Prompt/errors/NotPromptOwnerError';
import {
  ChangePromptTargetAIApiViewModel
} from '@src/server/PromptManagement/presentation/api/ChangePromptTarget/ChangePromptTargetAIApiViewModel';

export class ChangePromptTargetAIApiPresenter implements ChangePromptTargetAIPresenter {
  private viewModel: ChangePromptTargetAIApiViewModel | null = null;

  getViewModel(): ChangePromptTargetAIApiViewModel {
    return this.viewModel!;
  }

  displayPromptTargetAIChanged(response: ChangePromptTargetAIResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId, targetAI: response.targetAI}};
  }

  notifyPromptNotFound(error: PromptNotFoundError): void {
    this.viewModel = {status: 404, body: {error: error.message}};
  }

  notifyNotPromptOwner(error: NotPromptOwnerError): void {
    this.viewModel = {status: 403, body: {error: error.message}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
