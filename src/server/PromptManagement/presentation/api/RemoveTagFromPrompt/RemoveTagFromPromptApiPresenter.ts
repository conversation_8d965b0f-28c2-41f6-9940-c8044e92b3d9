import {
  RemoveTagFromPromptPresenter
} from '@src/server/PromptManagement/application/port/driven/RemoveTagFromPromptPresenter';
import {
  RemoveTagFromPromptResponse
} from '@src/server/PromptManagement/application/usecase/RemoveTagFromPrompt/RemoveTagFromPromptResponse';
import {
  RemoveTagFromPromptApiViewModel
} from '@src/server/PromptManagement/presentation/api/RemoveTagFromPrompt/RemoveTagFromPromptApiViewModel';

export class RemoveTagFromPromptApiPresenter implements RemoveTagFromPromptPresenter {
  private viewModel: RemoveTagFromPromptApiViewModel | null = null;

  getViewModel(): RemoveTagFromPromptApiViewModel {
    return this.viewModel!;
  }

  presentTagSuccessfullyRemoved(response: RemoveTagFromPromptResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId, tag: response.tag}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyTagNotFound(promptId: string, tag: string): void {
    this.viewModel = {status: 404, body: {error: `Tag not found for prompt ${promptId}: ${tag}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
