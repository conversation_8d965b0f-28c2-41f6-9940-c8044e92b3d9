import {
  RemoveTagFromPromptUseCase
} from "@src/server/PromptManagement/application/port/driving/RemoveTagFromPromptUseCase";
import {
  RemoveTagFromPromptApiPresenter
} from '@src/server/PromptManagement/presentation/api/RemoveTagFromPrompt/RemoveTagFromPromptApiPresenter';
import {
  RemoveTagFromPromptApiViewModel
} from '@src/server/PromptManagement/presentation/api/RemoveTagFromPrompt/RemoveTagFromPromptApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {RemoveTagFromPromptApiRequest} from './RemoveTagFromPromptApiRequest';

export class RemoveTagFromPromptApiController {
  private readonly usecase: RemoveTagFromPromptUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: RemoveTagFromPromptUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: RemoveTagFromPromptApiRequest): Promise<RemoveTagFromPromptApiViewModel> {
    const presenter = new RemoveTagFromPromptApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
