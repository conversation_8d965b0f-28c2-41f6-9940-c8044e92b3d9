import {ArchivePromptPresenter} from '@src/server/PromptManagement/application/port/driven/ArchivePromptPresenter';
import {
  ArchivePromptResponse
} from '@src/server/PromptManagement/application/usecase/ArchivePrompt/ArchivePromptResponse';
import {ArchivePromptApiViewModel} from '@src/server/PromptManagement/presentation/api/ArchivePrompt/ArchivePromptApiViewModel';

export class ArchivePromptApiPresenter implements ArchivePromptPresenter {
  private viewModel: ArchivePromptApiViewModel | null = null;

  getViewModel(): ArchivePromptApiViewModel {
    return this.viewModel!;
  }

  presentPromptSuccessfullyArchived(response: ArchivePromptResponse): void {
    this.viewModel = {status: 200, body: {promptId: response.promptId, isArchived: response.isArchived}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyPromptAlreadyArchived(promptId: string): void {
    this.viewModel = {status: 409, body: {error: `Prompt already archived: ${promptId}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
