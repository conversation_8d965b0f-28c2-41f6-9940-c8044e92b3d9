import { ArchivePromptUseCase } from "@src/server/PromptManagement/application/port/driving/ArchivePromptUseCase";
import { ArchivePromptApiPresenter } from '@src/server/PromptManagement/presentation/api/ArchivePrompt/ArchivePromptApiPresenter';
import { ArchivePromptApiViewModel } from '@src/server/PromptManagement/presentation/api/ArchivePrompt/ArchivePromptApiViewModel';
import { AuthenticationGateway } from '@src/server/Authentication';
import {ArchivePromptApiRequest} from './ArchivePromptApiRequest';

export class ArchivePromptApiController {
  private readonly usecase: ArchivePromptUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: ArchivePromptUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: ArchivePromptApiRequest): Promise<ArchivePromptApiViewModel> {
    const presenter = new ArchivePromptApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
