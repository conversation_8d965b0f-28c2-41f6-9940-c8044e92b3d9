import {DuplicatePromptPresenter} from '@src/server/PromptManagement/application/port/driven/DuplicatePromptPresenter';
import {
  DuplicatePromptResponse
} from '@src/server/PromptManagement/application/usecase/DuplicatePrompt/DuplicatePromptResponse';
import {DuplicatePromptApiViewModel} from '@src/server/PromptManagement/presentation/api/DuplicatePrompt/DuplicatePromptApiViewModel';

export class DuplicatePromptApiPresenter implements DuplicatePromptPresenter {
  private viewModel: DuplicatePromptApiViewModel | null = null;

  getViewModel(): DuplicatePromptApiViewModel {
    return this.viewModel!;
  }

  presentDuplicatedPrompt(response: DuplicatePromptResponse): void {
    this.viewModel = {status: 201, body: {promptId: response.promptId}};
  }

  notifyPromptNotFound(promptId: string): void {
    this.viewModel = {status: 404, body: {error: `Prompt not found: ${promptId}`}};
  }

  notifyNotPromptOwner(promptId: string): void {
    this.viewModel = {status: 403, body: {error: `Not owner of prompt: ${promptId}`}};
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = {status: 401, body: {error: 'Not authenticated'}};
  }
}
