import {DuplicatePromptUseCase} from "@src/server/PromptManagement/application/port/driving/DuplicatePromptUseCase";
import {
  DuplicatePromptApiPresenter
} from '@src/server/PromptManagement/presentation/api/DuplicatePrompt/DuplicatePromptApiPresenter';
import {DuplicatePromptApiViewModel} from '@src/server/PromptManagement/presentation/api/DuplicatePrompt/DuplicatePromptApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {DuplicatePromptApiRequest} from './DuplicatePromptApiRequest';

export class DuplicatePromptApiController {
  private readonly usecase: DuplicatePromptUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: DuplicatePromptUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: DuplicatePromptApiRequest): Promise<DuplicatePromptApiViewModel> {
    const presenter = new DuplicatePromptApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({ ...request, userId }, presenter);
    return presenter.getViewModel();
  }
}
