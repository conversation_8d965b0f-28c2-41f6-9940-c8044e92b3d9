import {PromptRepository} from '../../port/driven/PromptRepository';
import {IdentityProvider} from '@src/server/Shared/application/port/IdentityProvider';
import {DuplicatePromptRequest} from './DuplicatePromptRequest';
import {DuplicatePromptPresenter} from '../../port/driven/DuplicatePromptPresenter';
import {DuplicatePromptUseCase} from "@src/server/PromptManagement/application/port/driving/DuplicatePromptUseCase";

export class DuplicatePrompt implements DuplicatePromptUseCase {
  private readonly repository: PromptRepository;
  private readonly identityProvider: IdentityProvider;

  constructor(repository: PromptRepository, identityProvider: IdentityProvider) {
    this.identityProvider = identityProvider;
    this.repository = repository;
  }

  async execute(request: DuplicatePromptRequest, presenter: DuplicatePromptPresenter) {
    const {promptId, userId} = request;
    const originalPrompt = await this.repository.findById(promptId);

    if (!originalPrompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (originalPrompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }

    const copiedPromptId = this.identityProvider.generateId();
    const copiedPrompt = originalPrompt.duplicate(copiedPromptId);
    await this.repository.save(copiedPrompt);

    presenter.presentDuplicatedPrompt({promptId: copiedPromptId});
  }
}
