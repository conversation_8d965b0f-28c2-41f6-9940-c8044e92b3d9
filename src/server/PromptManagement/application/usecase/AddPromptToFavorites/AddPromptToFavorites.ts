import {PromptRepository} from '@src/server/PromptManagement/application/port/driven/PromptRepository';
import {
  AddPromptToFavoritesRequest
} from '@src/server/PromptManagement/application/usecase/AddPromptToFavorites/AddPromptToFavoritesRequest';
import {
  AddPromptToFavoritesPresenter
} from '@src/server/PromptManagement/application/port/driven/AddPromptToFavoritesPresenter';
import {
  AddPromptToFavoritesUseCase
} from "@src/server/PromptManagement/application/port/driving/AddPromptToFavoritesUseCase";

export class AddPromptToFavorites implements AddPromptToFavoritesUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: AddPromptToFavoritesRequest, presenter: AddPromptToFavoritesPresenter): Promise<void> {
    const {promptId, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }
    if (prompt.isInFavorite()) {
      presenter.notifyPromptAlreadyInFavorite(promptId);
      return;
    }

    prompt.addToFavorites();
    await this.repository.save(prompt);

    presenter.presentPromptSuccessfullyAddedToFavorites({promptId});
  }
}
