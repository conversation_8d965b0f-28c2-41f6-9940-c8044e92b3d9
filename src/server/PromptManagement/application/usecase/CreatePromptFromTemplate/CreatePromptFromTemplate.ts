import {PromptRepository} from '../../port/driven/PromptRepository';
import {CreatePromptFromTemplateRequest} from './CreatePromptFromTemplateRequest';
import {CreatePromptFromTemplatePresenter} from '../../port/driven/CreatePromptFromTemplatePresenter';
import {CreatePromptFromTemplateUseCase} from "@src/server/PromptManagement/application/port/driving/CreatePromptFromTemplateUseCase";
import {IdentityProvider} from "@src/server/Shared/application/port/IdentityProvider";
import {Prompt} from "@src/server/PromptManagement/domain/Prompt/Prompt";

export class CreatePromptFromTemplate implements CreatePromptFromTemplateUseCase {
  private readonly repository: PromptRepository;
  private readonly identityProvider: IdentityProvider;

  constructor(repository: PromptRepository, identityProvider: IdentityProvider) {
    this.repository = repository;
    this.identityProvider = identityProvider;
  }

  async execute(request: CreatePromptFromTemplateRequest, presenter: Create<PERSON>rom<PERSON><PERSON>romTemplatePresenter): Promise<void> {
    const {templateId, userId} = request;
    const template = await this.repository.findById(templateId);

    if (!template) {
      presenter.notifyPromptNotFound(templateId);
      return;
    }
    if (!template.isATemplate()) {
      presenter.notifyPromptIsNotATemplate(templateId);
      return;
    }

    const newPromptId = this.identityProvider.generateId();
    const snapshot = template.toSnapshot();

    const newPrompt = Prompt.fromSnapshot({
      ...snapshot,
      id: newPromptId,
      ownerId: userId,
      isTemplate: false,
      favorite: false,
      archived: false,
      status: 'draft',
    });

    await this.repository.save(newPrompt);

    presenter.presentPromptCreatedFromTemplate({promptId: newPromptId});
  }
}