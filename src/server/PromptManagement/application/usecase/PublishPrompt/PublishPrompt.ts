import {PromptRepository} from '../../port/driven/PromptRepository';
import {PublishPromptRequest} from './PublishPromptRequest';
import {PublishPromptPresenter} from '../../port/driven/PublishPromptPresenter';
import {PublishPromptUseCase} from "@src/server/PromptManagement/application/port/driving/PublishPromptUseCase";

export class PublishPrompt implements PublishPromptUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: PublishPromptRequest, presenter: PublishPromptPresenter): Promise<void> {
    const {promptId, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }
    if (prompt.isPublished()) {
      presenter.notifyPromptAlreadyPublished(promptId);
      return;
    }

    prompt.publish();
    await this.repository.save(prompt);

    presenter.displayPromptPublished({promptId, status: 'published'});
  }
}
