import {PromptRepository} from '../../port/driven/PromptRepository';
import {MarkPromptAsTemplateRequest} from './MarkPromptAsTemplateRequest';
import {MarkPromptAsTemplatePresenter} from '../../port/driven/MarkPromptAsTemplatePresenter';
import {MarkPromptAsTemplateUseCase} from "@src/server/PromptManagement/application/port/driving/MarkPromptAsTemplateUseCase";

export class MarkPromptAsTemplate implements MarkPromptAsTemplateUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: MarkPromptAsTemplateRequest, presenter: Mark<PERSON>romptAsTemplatePresenter): Promise<void> {
    const {promptId, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }

    prompt.markAsTemplate();
    await this.repository.save(prompt);

    presenter.presentPromptMarkedAsTemplate({promptId});
  }
}