import {PromptRepository} from '../../port/driven/PromptRepository';
import {RemovePromptFromFavoritesRequest} from './RemovePromptFromFavoritesRequest';
import {RemovePromptFromFavoritesPresenter} from '../../port/driven/RemovePromptFromFavoritesPresenter';
import {RemovePromptFromFavoritesUseCase} from "@src/server/PromptManagement/application/port/driving/RemovePromptFromFavoritesUseCase";

export class RemovePromptFromFavorites implements RemovePromptFromFavoritesUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: RemovePromptFromFavoritesRequest, presenter: RemovePromptFromFavoritesPresenter): Promise<void> {
    const {promptId, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }
    if (prompt.isNotInFavorite()) {
      presenter.notifyPromptNotInFavorite(promptId);
      return;
    }

    prompt.removeFromFavorites();
    await this.repository.save(prompt);

    presenter.presentPromptRemovedFromFavorites({promptId, isFavorite: false});
  }
}

