import {PromptRepository} from '../../port/driven/PromptRepository';
import {ArchivePromptRequest} from './ArchivePromptRequest';
import {ArchivePromptPresenter} from '../../port/driven/ArchivePromptPresenter';
import {ArchivePromptUseCase} from "@src/server/PromptManagement/application/port/driving/ArchivePromptUseCase";

export class ArchivePrompt implements ArchivePromptUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: ArchivePromptRequest, presenter: ArchivePromptPresenter): Promise<void> {
    const {promptId, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }
    if (prompt.isArchived()) {
      presenter.notifyPromptAlreadyArchived(promptId);
      return;
    }

    prompt.archive();
    await this.repository.save(prompt);

    presenter.presentPromptSuccessfullyArchived({promptId, isArchived: true});
  }
}
