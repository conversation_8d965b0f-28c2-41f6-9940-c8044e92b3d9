import {PromptRepository} from '../../port/driven/PromptRepository';
import {UnmarkPromptAsTemplateRequest} from './UnmarkPromptAsTemplateRequest';
import {UnmarkPromptAsTemplatePresenter} from '../../port/driven/UnmarkPromptAsTemplatePresenter';
import {UnmarkPromptAsTemplateUseCase} from "@src/server/PromptManagement/application/port/driving/UnmarkPromptAsTemplateUseCase";

export class UnmarkPromptAsTemplate implements UnmarkPromptAsTemplateUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: UnmarkPromptAsTemplateRequest, presenter: UnmarkPromptAsTemplatePresenter): Promise<void> {
    const {promptId, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }

    prompt.unmarkAsTemplate();
    await this.repository.save(prompt);

    presenter.presentPromptUnmarkedAsTemplate({promptId});
  }
}