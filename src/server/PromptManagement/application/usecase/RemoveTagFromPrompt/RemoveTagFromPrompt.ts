import {PromptRepository} from '../../port/driven/PromptRepository';
import {RemoveTagFromPromptRequest} from './RemoveTagFromPromptRequest';
import {RemoveTagFromPromptPresenter} from '../../port/driven/RemoveTagFromPromptPresenter';
import {RemoveTagFromPromptUseCase} from "@src/server/PromptManagement/application/port/driving/RemoveTagFromPromptUseCase";

export class RemoveTagFromPrompt implements RemoveTagFromPromptUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: RemoveTagFromPromptRequest, presenter: RemoveTagFromPromptPresenter): Promise<void> {
    const {promptId, tag, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }
    if (prompt.hasNotTag(tag)) {
      presenter.notifyTagNotFound(promptId, tag);
      return;
    }

    prompt.removeTag(tag);
    await this.repository.save(prompt);

    presenter.presentTagSuccessfullyRemoved({promptId, tag});
  }
}
