import {PromptRepository} from '../../port/driven/PromptRepository';
import {IdentityProvider} from '@src/server/Shared/application/port/IdentityProvider';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {CreatePromptDraftRequest} from './CreatePromptDraftRequest';
import {CreatePromptDraftPresenter} from '../../port/driven/CreatePromptDraftPresenter';
import {CreatePromptDraftUseCase} from "@src/server/PromptManagement/application/port/driving/CreatePromptDraftUseCase";

export class CreatePromptDraft implements CreatePromptDraftUseCase {
  private readonly repository: PromptRepository;
  private readonly identityProvider: IdentityProvider;

  constructor(repository: PromptRepository, identityProvider: IdentityProvider) {
    this.identityProvider = identityProvider;
    this.repository = repository;
  }

  async execute(request: CreatePromptDraftRe<PERSON>, presenter: <PERSON>reate<PERSON>rompt<PERSON>raft<PERSON>resenter,): Promise<void> {
    const {content, targetAI, authorId} = request;
    const promptId = this.identityProvider.generateId();
    const prompt = Prompt.createDraft(promptId, content, targetAI, authorId);

    await this.repository.save(prompt);

    presenter.presentPromptDraftSuccessfullyCreated({promptId});
  }
}
