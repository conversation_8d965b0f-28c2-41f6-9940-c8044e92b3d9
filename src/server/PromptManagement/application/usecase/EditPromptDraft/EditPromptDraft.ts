import {PromptRepository} from '../../port/driven/PromptRepository';
import {EditPromptDraftRequest} from './EditPromptDraftRequest';
import {EditPromptDraftPresenter} from '../../port/driven/EditPromptDraftPresenter';
import {EditPromptDraftUseCase} from "@src/server/PromptManagement/application/port/driving/EditPromptDraftUseCase";

export class EditPromptDraft implements EditPromptDraftUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: EditPromptDraftRequest, presenter: EditPromptDraftPresenter): Promise<void> {
    const {promptId, content, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }

    prompt.editDraft(content);
    await this.repository.save(prompt);

    presenter.presentPromptDraftEditedSuccessfully({promptId, content});
  }
}
