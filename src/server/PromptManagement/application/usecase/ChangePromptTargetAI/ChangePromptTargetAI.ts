import {PromptRepository} from '../../port/driven/PromptRepository';
import {ChangePromptTargetAIRequest} from './ChangePromptTargetAIRequest';
import {ChangePromptTargetAIPresenter} from '../../port/driven/ChangePromptTargetAIPresenter';
import {ChangePromptTargetAIUseCase} from "@src/server/PromptManagement/application/port/driving/ChangePromptTargetAIUseCase";
import {PromptNotFoundError} from '@src/server/PromptManagement/domain/Prompt/errors/PromptNotFoundError';
import {NotPromptOwnerError} from '@src/server/PromptManagement/domain/Prompt/errors/NotPromptOwnerError';

export class ChangePromptTargetAI implements ChangePromptTargetAIUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: ChangePromptTargetAIRequest, presenter: ChangePromptTargetAIPresenter): Promise<void> {
    const {promptId, targetAI, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(new PromptNotFoundError(promptId));
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(new NotPromptOwnerError(promptId));
      return;
    }

    prompt.changeTargetAI(targetAI);
    await this.repository.save(prompt);

    presenter.displayPromptTargetAIChanged({promptId, targetAI});
  }
}
