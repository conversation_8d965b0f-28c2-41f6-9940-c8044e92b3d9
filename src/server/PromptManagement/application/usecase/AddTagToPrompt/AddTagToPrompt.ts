import {PromptRepository} from '../../port/driven/PromptRepository';
import {AddTagToPromptRequest} from './AddTagToPromptRequest';
import {AddTagToPromptPresenter} from '../../port/driven/AddTagToPromptPresenter';
import {AddTagToPromptUseCase} from "@src/server/PromptManagement/application/port/driving/AddTagToPromptUseCase";

export class AddTagToPrompt implements AddTagToPromptUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: AddTagToPromptRequest, presenter: AddTagToPromptPresenter): Promise<void> {
    const {promptId, userId, tag} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }
    if (prompt.hasTag(tag)) {
      presenter.notifyTagAlreadyExists(promptId, tag);
      return;
    }

    prompt.addTag(tag);
    await this.repository.save(prompt);

    presenter.presentTagAddedSuccessfully({promptId, tag});
  }
}
