import {PromptRepository} from '../../port/driven/PromptRepository';
import {DeletePromptRequest} from './DeletePromptRequest';
import {DeletePromptPresenter} from '../../port/driven/DeletePromptPresenter';
import {DeletePromptUseCase} from "@src/server/PromptManagement/application/port/driving/DeletePromptUseCase";

export class DeletePrompt implements DeletePromptUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: DeletePromptRequest, presenter: DeletePromptPresenter): Promise<void> {
    const {promptId, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }

    await this.repository.delete(promptId);

    presenter.displayPromptDeleted({promptId});
  }
}
