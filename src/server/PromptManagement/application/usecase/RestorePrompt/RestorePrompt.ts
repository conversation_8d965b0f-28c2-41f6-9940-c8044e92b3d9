import {PromptRepository} from '../../port/driven/PromptRepository';
import {RestorePromptRequest} from './RestorePromptRequest';
import {RestorePromptPresenter} from '../../port/driven/RestorePromptPresenter';
import {RestorePromptUseCase} from "@src/server/PromptManagement/application/port/driving/RestorePromptUseCase";

export class RestorePrompt implements RestorePromptUseCase {
  private readonly repository: PromptRepository;

  constructor(repository: PromptRepository) {
    this.repository = repository;
  }

  async execute(request: RestorePromptRequest, presenter: RestorePromptPresenter): Promise<void> {
    const {promptId, userId} = request;
    const prompt = await this.repository.findById(promptId);

    if (!prompt) {
      presenter.notifyPromptNotFound(promptId);
      return;
    }
    if (prompt.hasNotOwner(userId)) {
      presenter.notifyNotPromptOwner(promptId);
      return;
    }
    if (prompt.isNotArchived()) {
      presenter.notifyPromptNotArchived(promptId);
      return;
    }

    prompt.restore();
    await this.repository.save(prompt);

    presenter.presentPromptSuccessfullyRestored({promptId});
  }
}
