import {MarkPromptAsTemplateRequest} from "@src/server/PromptManagement/application/usecase/MarkPromptAsTemplate/MarkPromptAsTemplateRequest";
import {MarkPromptAsTemplatePresenter} from "@src/server/PromptManagement/application/port/driven/MarkPromptAsTemplatePresenter";

export interface MarkPromptAsTemplateUseCase {
  execute(request: MarkPromptAsTemplateRequest, presenter: MarkPromptAsTemplatePresenter): Promise<void>;
}