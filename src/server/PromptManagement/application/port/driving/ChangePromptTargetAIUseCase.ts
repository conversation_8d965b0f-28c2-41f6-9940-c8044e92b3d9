import { ChangePromptTargetAIRequest } from "@src/server/PromptManagement/application/usecase/ChangePromptTargetAI/ChangePromptTargetAIRequest";
import { ChangePromptTargetAIPresenter } from "@src/server/PromptManagement/application/port/driven/ChangePromptTargetAIPresenter";

export interface ChangePromptTargetAIUseCase {
  execute(request: ChangePromptTargetAIRequest, presenter: ChangePromptTargetAIPresenter): Promise<void>;
}
