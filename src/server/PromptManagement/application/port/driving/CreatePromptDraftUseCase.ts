import { CreatePromptDraftRequest } from "@src/server/PromptManagement/application/usecase/CreatePromptDraft/CreatePromptDraftRequest";
import { CreatePromptDraftPresenter } from "@src/server/PromptManagement/application/port/driven/CreatePromptDraftPresenter";

export interface CreatePromptDraftUseCase {
  execute(request: CreatePromptDraftRequest, presenter: Create<PERSON>romptDraftPresenter): Promise<void>;
}
