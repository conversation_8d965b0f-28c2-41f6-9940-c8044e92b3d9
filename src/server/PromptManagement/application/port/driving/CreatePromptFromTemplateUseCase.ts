import {CreatePromptFromTemplateRequest} from "@src/server/PromptManagement/application/usecase/CreatePromptFromTemplate/CreatePromptFromTemplateRequest";
import {CreatePromptFromTemplatePresenter} from "@src/server/PromptManagement/application/port/driven/CreatePromptFromTemplatePresenter";

export interface CreatePromptFromTemplateUseCase {
  execute(request: CreatePromptFromTemplateRequest, presenter: CreatePromptFromTemplatePresenter): Promise<void>;
}