import {ChangePromptTargetAIResponse} from '@src/server/PromptManagement/application/usecase/ChangePromptTargetAI/ChangePromptTargetAIResponse';
import {PromptNotFoundError} from '@src/server/PromptManagement/domain/Prompt/errors/PromptNotFoundError';
import {NotPromptOwnerError} from '@src/server/PromptManagement/domain/Prompt/errors/NotPromptOwnerError';

export interface ChangePromptTargetAIPresenter {
  displayPromptTargetAIChanged(response: ChangePromptTargetAIResponse): void;
  notifyPromptNotFound(error: PromptNotFoundError): void;
  notifyNotPromptOwner(error: NotPromptOwnerError): void;

  notifyUserNotAuthenticated(): void;
}
