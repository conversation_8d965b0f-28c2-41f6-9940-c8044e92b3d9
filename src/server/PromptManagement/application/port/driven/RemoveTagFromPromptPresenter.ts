import {RemoveTagFromPromptResponse} from '@src/server/PromptManagement/application/usecase/RemoveTagFromPrompt/RemoveTagFromPromptResponse';

export interface RemoveTagFromPromptPresenter {
  presentTagSuccessfullyRemoved({promptId, tag}: RemoveTagFromPromptResponse): void;

  notifyPromptNotFound(promptId: string): void;

  notifyNotPromptOwner(promptId: string): void;

  notifyTagNotFound(promptId: string, tag: string): void;

  notifyUserNotAuthenticated(): void;
}
