import {RemovePromptFromFavoritesResponse} from '@src/server/PromptManagement/application/usecase/RemovePromptFromFavorites/RemovePromptFromFavoritesResponse';

export interface RemovePromptFromFavoritesPresenter {
  presentPromptRemovedFromFavorites(response: RemovePromptFromFavoritesResponse): void;
  notifyPromptNotFound(promptId: string): void;
  notifyNotPromptOwner(promptId: string): void;
  notifyPromptNotInFavorite(promptId: string): void;

  notifyUserNotAuthenticated(): void;
}
