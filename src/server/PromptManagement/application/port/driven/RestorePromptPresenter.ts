import {
  RestorePromptResponse
} from "@src/server/PromptManagement/application/usecase/RestorePrompt/RestorePromptResponse";

export interface RestorePromptPresenter {
  presentPromptSuccessfullyRestored(response: RestorePromptResponse): void;

  notifyPromptNotFound(promptId: string): void;

  notifyNotPromptOwner(promptId: string): void;

  notifyPromptNotArchived(promptId: string): void;

  notifyUserNotAuthenticated(): void;
}
