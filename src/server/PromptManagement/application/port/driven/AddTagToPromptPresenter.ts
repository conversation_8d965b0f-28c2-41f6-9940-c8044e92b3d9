import {AddTagToPromptResponse} from '@src/server/PromptManagement/application/usecase/AddTagToPrompt/AddTagToPromptResponse';

export interface AddTagToPromptPresenter {
  presentTagAddedSuccessfully(response: AddTagToPromptResponse): void;

  notifyPromptNotFound(promptId: string): void;

  notifyNotPromptOwner(promptId: string): void;

  notifyTagAlreadyExists(promptId: string, tag: string): void;

  notifyUserNotAuthenticated(): void;
}
