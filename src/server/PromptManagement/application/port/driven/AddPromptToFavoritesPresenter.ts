import {
  AddPromptToFavoritesResponse
} from "@src/server/PromptManagement/application/usecase/AddPromptToFavorites/AddPromptToFavoritesResponse";

export interface AddPromptToFavoritesPresenter {
  presentPromptSuccessfullyAddedToFavorites(response: AddPromptToFavoritesResponse): void;

  notifyPromptNotFound(promptId: string): void;

  notifyNotPromptOwner(promptId: string): void;

  notifyPromptAlreadyInFavorite(promptId: string): void;

  notifyUserNotAuthenticated(): void;
}
