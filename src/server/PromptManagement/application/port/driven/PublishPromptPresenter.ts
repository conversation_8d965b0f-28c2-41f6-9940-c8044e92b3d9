import {PublishPromptResponse} from '@src/server/PromptManagement/application/usecase/PublishPrompt/PublishPromptResponse';

export interface PublishPromptPresenter {
  displayPromptPublished(response: PublishPromptResponse): void;

  notifyPromptNotFound(promptId: string): void;

  notifyNotPromptOwner(promptId: string): void;

  notifyPromptAlreadyPublished(promptId: string): void;

  notifyUserNotAuthenticated(): void;
}
