import {PromptRepository} from '@src/server/PromptManagement/application/port/driven/PromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';

export class InMemoryPromptRepository implements PromptRepository {
  private readonly prompts = new Map<string, Prompt>();

  async findById(id: string): Promise<Prompt | null> {
    return this.prompts.get(id) ?? null;
  }

  async save(prompt: Prompt): Promise<void> {
    this.prompts.set(prompt.getId(), prompt);
  }

  async delete(id: string): Promise<void> {
    this.prompts.delete(id);
  }
}
