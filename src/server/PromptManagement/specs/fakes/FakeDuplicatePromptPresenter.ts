import {DuplicatePromptPresenter} from "../../application/port/driven/DuplicatePromptPresenter";
import {DuplicatePromptResponse} from "../../application/usecase/DuplicatePrompt/DuplicatePromptResponse";

export class FakeDuplicatePromptPresenter implements DuplicatePromptPresenter {
  response: DuplicatePromptResponse | null = null;
  errorMessage: string | null = null;

  presentDuplicatedPrompt(response: DuplicatePromptResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}