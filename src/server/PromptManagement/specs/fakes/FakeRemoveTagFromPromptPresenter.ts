import {RemoveTagFromPromptPresenter} from "../../application/port/driven/RemoveTagFromPromptPresenter";
import {RemoveTagFromPromptResponse} from "../../application/usecase/RemoveTagFromPrompt/RemoveTagFromPromptResponse";

export class FakeRemoveTagFromPromptPresenter implements RemoveTagFromPromptPresenter {
  response: RemoveTagFromPromptResponse | null = null;
  errorMessage: string | null = null;

  presentTagSuccessfullyRemoved(response: RemoveTagFromPromptResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyTagNotFound(promptId: string, tag: string): void {
    this.response = null;
    this.errorMessage = `Tag not found for prompt ${promptId}: ${tag}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}