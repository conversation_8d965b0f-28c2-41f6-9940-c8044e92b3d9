import {AddPromptToFavoritesPresenter} from "../../application/port/driven/AddPromptToFavoritesPresenter";
import {
  AddPromptToFavoritesResponse
} from "../../application/usecase/AddPromptToFavorites/AddPromptToFavoritesResponse";

export class FakeAddPromptToFavoritesPresenter implements AddPromptToFavoritesPresenter {
  response: AddPromptToFavoritesResponse | null = null;
  errorMessage: string | null = null;

  presentPromptSuccessfullyAddedToFavorites(response: AddPromptToFavoritesResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyPromptAlreadyInFavorite(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt already in favorites: ${promptId}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}