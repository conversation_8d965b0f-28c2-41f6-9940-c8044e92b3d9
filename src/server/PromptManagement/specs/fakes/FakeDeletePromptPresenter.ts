import {DeletePromptPresenter} from "../../application/port/driven/DeletePromptPresenter";
import {DeletePromptResponse} from "../../application/usecase/DeletePrompt/DeletePromptResponse";

export class FakeDeletePromptPresenter implements DeletePromptPresenter {
  response: DeletePromptResponse | null = null;
  errorMessage: string | null = null;

  displayPromptDeleted(response: DeletePromptResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}