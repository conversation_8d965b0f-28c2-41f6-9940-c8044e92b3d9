import {EditPromptDraftPresenter} from "../../application/port/driven/EditPromptDraftPresenter";
import {EditPromptDraftResponse} from "../../application/usecase/EditPromptDraft/EditPromptDraftResponse";

export class FakeEditPromptDraftPresenter implements EditPromptDraftPresenter {
  response: EditPromptDraftResponse | null = null;
  errorMessage: string | null = null;

  presentPromptDraftEditedSuccessfully(response: EditPromptDraftResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}