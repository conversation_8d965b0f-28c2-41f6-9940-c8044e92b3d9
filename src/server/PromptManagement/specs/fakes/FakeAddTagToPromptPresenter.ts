import {AddTagToPromptPresenter} from "../../application/port/driven/AddTagToPromptPresenter";
import {AddTagToPromptResponse} from "../../application/usecase/AddTagToPrompt/AddTagToPromptResponse";

export class FakeAddTagToPromptPresenter implements AddTagToPromptPresenter {
  response: AddTagToPromptResponse | null = null;
  errorMessage: string | null = null;

  presentTagAddedSuccessfully(response: AddTagToPromptResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyTagAlreadyExists(promptId: string, tag: string): void {
    this.response = null;
    this.errorMessage = `Tag already exists for prompt ${promptId}: ${tag}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}