import {ChangePromptTargetAIPresenter} from "../../application/port/driven/ChangePromptTargetAIPresenter";
import {
  ChangePromptTargetAIResponse
} from "../../application/usecase/ChangePromptTargetAI/ChangePromptTargetAIResponse";
import {PromptNotFoundError} from "../../domain/Prompt/errors/PromptNotFoundError";
import {NotPromptOwnerError} from "../../domain/Prompt/errors/NotPromptOwnerError";

export class FakeChangePromptTargetAIPresenter implements ChangePromptTargetAIPresenter {
  response: ChangePromptTargetAIResponse | null = null;
  errorMessage: string | null = null;

  displayPromptTargetAIChanged(response: ChangePromptTargetAIResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(error: PromptNotFoundError): void {
    this.response = null;
    this.errorMessage = error.message;
  }

  notifyNotPromptOwner(error: NotPromptOwnerError): void {
    this.response = null;
    this.errorMessage = error.message;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}