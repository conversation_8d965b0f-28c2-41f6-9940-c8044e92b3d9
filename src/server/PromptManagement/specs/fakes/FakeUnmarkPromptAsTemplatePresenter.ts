import {UnmarkPromptAsTemplatePresenter} from "@src/server/PromptManagement/application/port/driven/UnmarkPromptAsTemplatePresenter";
import {UnmarkPromptAsTemplateResponse} from "@src/server/PromptManagement/application/usecase/UnmarkPromptAsTemplate/UnmarkPromptAsTemplateResponse";

export class FakeUnmarkPromptAsTemplatePresenter implements UnmarkPromptAsTemplatePresenter {
  response: UnmarkPromptAsTemplateResponse | null = null;
  errorMessage: string | null = null;

  presentPromptUnmarkedAsTemplate(response: UnmarkPromptAsTemplateResponse): void {
    this.response = response;
  }

  notifyPromptNotFound(promptId: string): void {
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }
}