import {ArchivePromptPresenter} from "../../application/port/driven/ArchivePromptPresenter";
import {ArchivePromptResponse} from "../../application/usecase/ArchivePrompt/ArchivePromptResponse";

export class FakeArchivePromptPresenter implements ArchivePromptPresenter {
  response: ArchivePromptResponse | null = null;
  errorMessage: string | null = null;

  presentPromptSuccessfullyArchived(response: ArchivePromptResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyPromptAlreadyArchived(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt already archived: ${promptId}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}