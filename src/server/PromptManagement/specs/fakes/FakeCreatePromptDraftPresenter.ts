import {CreatePromptDraftPresenter} from "../../application/port/driven/CreatePromptDraftPresenter";
import {CreatePromptDraftResponse} from "../../application/usecase/CreatePromptDraft/CreatePromptDraftResponse";

export class FakeCreatePromptDraftPresenter implements CreatePromptDraftPresenter {
  response: CreatePromptDraftResponse | null = null;
  errorMessage: string | null = null;

  presentPromptDraftSuccessfullyCreated(response: CreatePromptDraftResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}