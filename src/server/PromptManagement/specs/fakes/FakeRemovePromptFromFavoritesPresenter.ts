import {RemovePromptFromFavoritesPresenter} from "../../application/port/driven/RemovePromptFromFavoritesPresenter";
import {
  RemovePromptFromFavoritesResponse
} from "../../application/usecase/RemovePromptFromFavorites/RemovePromptFromFavoritesResponse";

export class FakeRemovePromptFromFavoritesPresenter implements RemovePromptFromFavoritesPresenter {
  response: RemovePromptFromFavoritesResponse | null = null;
  errorMessage: string | null = null;

  presentPromptRemovedFromFavorites(response: RemovePromptFromFavoritesResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyPromptNotInFavorite(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not in favorites: ${promptId}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}