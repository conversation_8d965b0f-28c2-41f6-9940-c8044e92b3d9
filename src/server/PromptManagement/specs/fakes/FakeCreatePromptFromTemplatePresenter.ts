import {CreatePromptFromTemplatePresenter} from "@src/server/PromptManagement/application/port/driven/CreatePromptFromTemplatePresenter";
import {CreatePromptFromTemplateResponse} from "@src/server/PromptManagement/application/usecase/CreatePromptFromTemplate/CreatePromptFromTemplateResponse";

export class FakeCreatePromptFromTemplatePresenter implements CreatePromptFromTemplatePresenter {
  response: CreatePromptFromTemplateResponse | null = null;
  errorMessage: string | null = null;

  presentPromptCreatedFromTemplate(response: CreatePromptFromTemplateResponse): void {
    this.response = response;
  }

  notifyPromptNotFound(promptId: string): void {
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyPromptIsNotATemplate(promptId: string): void {
    this.errorMessage = `Prompt is not a template: ${promptId}`;
  }
}