import {MarkPromptAsTemplatePresenter} from "@src/server/PromptManagement/application/port/driven/MarkPromptAsTemplatePresenter";
import {MarkPromptAsTemplateResponse} from "@src/server/PromptManagement/application/usecase/MarkPromptAsTemplate/MarkPromptAsTemplateResponse";

export class FakeMarkPromptAsTemplatePresenter implements MarkPromptAsTemplatePresenter {
  response: MarkPromptAsTemplateResponse | null = null;
  errorMessage: string | null = null;

  presentPromptMarkedAsTemplate(response: MarkPromptAsTemplateResponse): void {
    this.response = response;
  }

  notifyPromptNotFound(promptId: string): void {
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }
}