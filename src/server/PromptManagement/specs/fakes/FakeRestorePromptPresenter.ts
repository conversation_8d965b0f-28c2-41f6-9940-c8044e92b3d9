import {RestorePromptPresenter} from "../../application/port/driven/RestorePromptPresenter";
import {RestorePromptResponse} from "../../application/usecase/RestorePrompt/RestorePromptResponse";

export class FakeRestorePromptPresenter implements RestorePromptPresenter {
  response: RestorePromptResponse | null = null;
  errorMessage: string | null = null;

  presentPromptSuccessfullyRestored(response: RestorePromptResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyPromptNotArchived(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not archived: ${promptId}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}