import {PublishPromptPresenter} from "../../application/port/driven/PublishPromptPresenter";
import {PublishPromptResponse} from "../../application/usecase/PublishPrompt/PublishPromptResponse";

export class FakePublishPromptPresenter implements PublishPromptPresenter {
  response: PublishPromptResponse | null = null;
  errorMessage: string | null = null;

  displayPromptPublished(response: PublishPromptResponse): void {
    this.response = response;
    this.errorMessage = null;
  }

  notifyPromptNotFound(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt not found: ${promptId}`;
  }

  notifyNotPromptOwner(promptId: string): void {
    this.response = null;
    this.errorMessage = `Not owner of prompt: ${promptId}`;
  }

  notifyPromptAlreadyPublished(promptId: string): void {
    this.response = null;
    this.errorMessage = `Prompt already published: ${promptId}`;
  }

  notifyUserNotAuthenticated(): void {
    this.response = null;
    this.errorMessage = 'Not authenticated';
  }
}