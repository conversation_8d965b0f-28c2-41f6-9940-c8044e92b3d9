import {AddPromptToFavoritesApiPresenter} from '@src/server/PromptManagement/presentation/api/AddPromptToFavorites/AddPromptToFavoritesApiPresenter';

describe('AddPromptToFavoritesApiPresenter', () => {
  describe('When presenting a prompt successfully added to favorites', () => {
    it('should build a 200 view model with prompt id', () => {
      // Arrange
      const presenter = new AddPromptToFavoritesApiPresenter();

      // Act
      presenter.presentPromptSuccessfullyAddedToFavorites({promptId: 'p1'});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 200, body: {promptId: 'p1'}});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should build a 404 error view model', () => {
      // Arrange
      const presenter = new AddPromptToFavoritesApiPresenter();

      // Act
      presenter.notifyPromptNotFound('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: 'Prompt not found: p1'}});
    });
  });

  describe('When the user is not the owner', () => {
    it('should build a 403 error view model', () => {
      // Arrange
      const presenter = new AddPromptToFavoritesApiPresenter();

      // Act
      presenter.notifyNotPromptOwner('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 403, body: {error: 'Not owner of prompt: p1'}});
    });
  });

  describe('When the prompt is already in favorites', () => {
    it('should build a 409 Conflict error view model', () => {
      // Arrange
      const presenter = new AddPromptToFavoritesApiPresenter();

      // Act
      presenter.notifyPromptAlreadyInFavorite('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 409, body: {error: 'Prompt already in favorites: p1'}});
    });
  });
});
