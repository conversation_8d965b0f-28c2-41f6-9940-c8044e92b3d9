import {PublishPromptApiPresenter} from '@src/server/PromptManagement/presentation/api/PublishPrompt/PublishPromptApiPresenter';

describe('PublishPromptApiPresenter', () => {
  describe('When presenting a published prompt', () => {
    it('should build a 200 view model with prompt id and status', () => {
      // Arrange
      const presenter = new PublishPromptApiPresenter();

      // Act
      presenter.displayPromptPublished({promptId: 'p1', status: 'published'});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 200, body: {promptId: 'p1', status: 'published'}});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should build a 404 error view model', () => {
      // Arrange
      const presenter = new PublishPromptApiPresenter();

      // Act
      presenter.notifyPromptNotFound('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: 'Prompt not found: p1'}});
    });
  });

  describe('When the user is not the owner', () => {
    it('should build a 403 error view model', () => {
      // Arrange
      const presenter = new PublishPromptApiPresenter();

      // Act
      presenter.notifyNotPromptOwner('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 403, body: {error: 'Not owner of prompt: p1'}});
    });
  });

  describe('When the prompt is already published', () => {
    it('should build a 409 Conflict error view model', () => {
      // Arrange
      const presenter = new PublishPromptApiPresenter();

      // Act
      presenter.notifyPromptAlreadyPublished('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 409, body: {error: 'Prompt already published: p1'}});
    });
  });
});
