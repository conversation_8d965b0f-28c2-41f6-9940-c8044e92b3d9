import {AddTagToPromptApiPresenter} from '@src/server/PromptManagement/presentation/api/AddTagToPrompt/AddTagToPromptApiPresenter';

describe('AddTagToPromptApiPresenter', () => {
  describe('When presenting a tag successfully added', () => {
    it('should build a 200 view model with prompt id and tag', () => {
      // Arrange
      const presenter = new AddTagToPromptApiPresenter();

      // Act
      presenter.presentTagAddedSuccessfully({promptId: 'p1', tag: 't1'});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 200, body: {promptId: 'p1', tag: 't1'}});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should build a 404 error view model', () => {
      // Arrange
      const presenter = new AddTagToPromptApiPresenter();

      // Act
      presenter.notifyPromptNotFound('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: 'Prompt not found: p1'}});
    });
  });

  describe('When the user is not the owner', () => {
    it('should build a 403 error view model', () => {
      // Arrange
      const presenter = new AddTagToPromptApiPresenter();

      // Act
      presenter.notifyNotPromptOwner('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 403, body: {error: 'Not owner of prompt: p1'}});
    });
  });

  describe('When the tag already exists on the prompt', () => {
    it('should build a 409 Conflict error view model', () => {
      // Arrange
      const presenter = new AddTagToPromptApiPresenter();

      // Act
      presenter.notifyTagAlreadyExists('p1', 't1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 409, body: {error: 'Tag already exists for prompt p1: t1'}});
    });
  });
});
