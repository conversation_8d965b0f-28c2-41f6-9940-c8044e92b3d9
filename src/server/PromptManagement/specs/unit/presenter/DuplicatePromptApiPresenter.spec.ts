import {DuplicatePromptApiPresenter} from '@src/server/PromptManagement/presentation/api/DuplicatePrompt/DuplicatePromptApiPresenter';

describe('DuplicatePromptApiPresenter', () => {
  describe('When presenting a duplicated prompt', () => {
    it('should build a 201 view model with prompt id', () => {
      // Arrange
      const presenter = new DuplicatePromptApiPresenter();

      // Act
      presenter.presentDuplicatedPrompt({promptId: 'p1'});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 201, body: {promptId: 'p1'}});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should build a 404 error view model', () => {
      // Arrange
      const presenter = new DuplicatePromptApiPresenter();

      // Act
      presenter.notifyPromptNotFound('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: 'Prompt not found: p1'}});
    });
  });

  describe('When the user is not the owner of the prompt', () => {
    it('should build a 403 Forbidden error view model', () => {
      // Arrange
      const presenter = new DuplicatePromptApiPresenter();

      // Act
      presenter.notifyNotPromptOwner('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 403, body: {error: 'Not owner of prompt: p1'}});
    });
  });
});
