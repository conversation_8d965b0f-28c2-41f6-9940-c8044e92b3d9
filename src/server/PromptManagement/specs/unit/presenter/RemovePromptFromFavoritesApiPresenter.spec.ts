import {RemovePromptFromFavoritesApiPresenter} from '@src/server/PromptManagement/presentation/api/RemovePromptFromFavorites/RemovePromptFromFavoritesApiPresenter';

describe('RemovePromptFromFavoritesApiPresenter', () => {
  describe('When presenting a prompt removed from favorites', () => {
    it('should build a 200 view model with prompt id and favorite state', () => {
      // Arrange
      const presenter = new RemovePromptFromFavoritesApiPresenter();

      // Act
      presenter.presentPromptRemovedFromFavorites({promptId: 'p1', isFavorite: false});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 200, body: {promptId: 'p1', isFavorite: false}});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should build a 404 error view model', () => {
      // Arrange
      const presenter = new RemovePromptFromFavoritesApiPresenter();

      // Act
      presenter.notifyPromptNotFound('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: 'Prompt not found: p1'}});
    });
  });

  describe('When the user is not the owner', () => {
    it('should build a 403 error view model', () => {
      // Arrange
      const presenter = new RemovePromptFromFavoritesApiPresenter();

      // Act
      presenter.notifyNotPromptOwner('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 403, body: {error: 'Not owner of prompt: p1'}});
    });
  });

  describe('When the prompt is not in favorites', () => {
    it('should build a 409 Conflict error view model', () => {
      // Arrange
      const presenter = new RemovePromptFromFavoritesApiPresenter();

      // Act
      presenter.notifyPromptNotInFavorite('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 409, body: {error: 'Prompt not in favorites: p1'}});
    });
  });
});
