import {CreatePromptDraftApiPresenter} from '@src/server/PromptManagement/presentation/api/CreatePromptDraft/CreatePromptDraftApiPresenter';

describe('Given a presenter for creating a prompt draft', () => {

  describe('When a prompt draft is successfully created', () => {
    it('should build a 201 Created view model with the new prompt ID', () => {
      // Arrange
      const presenter = new CreatePromptDraftApiPresenter();

      // Act
      presenter.presentPromptDraftSuccessfullyCreated({promptId: 'p1'});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 201, body: {promptId: 'p1'}});
    });
  });
});
