import {RemoveTagFromPromptApiPresenter} from '@src/server/PromptManagement/presentation/api/RemoveTagFromPrompt/RemoveTagFromPromptApiPresenter';

describe('RemoveTagFromPromptApiPresenter', () => {
  describe('When presenting a tag removed from a prompt', () => {
    it('should build a 200 view model with prompt id and tag', () => {
      // Arrange
      const presenter = new RemoveTagFromPromptApiPresenter();

      // Act
      presenter.presentTagSuccessfullyRemoved({promptId: 'p1', tag: 't1'});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 200, body: {promptId: 'p1', tag: 't1'}});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should build a 404 error view model', () => {
      // Arrange
      const presenter = new RemoveTagFromPromptApiPresenter();

      // Act
      presenter.notifyPromptNotFound('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: 'Prompt not found: p1'}});
    });
  });

  describe('When the user is not the owner', () => {
    it('should build a 403 error view model', () => {
      // Arrange
      const presenter = new RemoveTagFromPromptApiPresenter();

      // Act
      presenter.notifyNotPromptOwner('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 403, body: {error: 'Not owner of prompt: p1'}});
    });
  });

  describe('When the tag is not found on the prompt', () => {
    it('should build a 404 Not Found error view model', () => {
      // Arrange
      const presenter = new RemoveTagFromPromptApiPresenter();

      // Act
      presenter.notifyTagNotFound('p1', 't1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: 'Tag not found for prompt p1: t1'}});
    });
  });
});
