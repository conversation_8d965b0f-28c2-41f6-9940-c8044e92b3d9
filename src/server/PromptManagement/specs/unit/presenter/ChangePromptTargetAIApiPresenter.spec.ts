import {ChangePromptTargetAIApiPresenter} from '@src/server/PromptManagement/presentation/api/ChangePromptTarget/ChangePromptTargetAIApiPresenter';
import {PromptNotFoundError} from '@src/server/PromptManagement/domain/Prompt/errors/PromptNotFoundError';
import {NotPromptOwnerError} from '@src/server/PromptManagement/domain/Prompt/errors/NotPromptOwnerError';

describe('Given a presenter for changing a prompt\'s target AI', () => {

  describe('When a prompt\'s target AI is successfully changed', () => {
    it('should build a 200 OK view model with the prompt ID and new target AI', () => {

      // Arrange
      const presenter = new ChangePromptTargetAIApiPresenter();

      // Act
      presenter.displayPromptTargetAIChanged({promptId: 'p1', targetAI: 'GPT-4'});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 200, body: {promptId: 'p1', targetAI: 'GPT-4'}});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should build a 404 error view model', () => {
      // Arrange
      const presenter = new ChangePromptTargetAIApiPresenter();
      const error = new PromptNotFoundError('p1');

      // Act
      presenter.notifyPromptNotFound(error);

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: error.message}});
    });
  });

  describe('When the user is not the owner', () => {
    it('should build a 403 error view model', () => {
      // Arrange
      const presenter = new ChangePromptTargetAIApiPresenter();
      const error = new NotPromptOwnerError('p1');

      // Act
      presenter.notifyNotPromptOwner(error);

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 403, body: {error: error.message}});
    });
  });
});
