import {ArchivePromptApiPresenter} from '@src/server/PromptManagement/presentation/api/ArchivePrompt/ArchivePromptApiPresenter';

describe('ArchivePromptApiPresenter', () => {
  describe('When presenting a prompt successfully archived', () => {
    it('should build a 200 view model with prompt id and archived state', () => {
      // Arrange
      const presenter = new ArchivePromptApiPresenter();

      // Act
      presenter.presentPromptSuccessfullyArchived({promptId: 'p1', isArchived: true});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 200, body: {promptId: 'p1', isArchived: true}});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should build a 404 error view model', () => {
      // Arrange
      const presenter = new ArchivePromptApiPresenter();

      // Act
      presenter.notifyPromptNotFound('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: 'Prompt not found: p1'}});
    });
  });

  describe('When the user is not the owner', () => {
    it('should build a 403 error view model', () => {
      // Arrange
      const presenter = new ArchivePromptApiPresenter();

      // Act
      presenter.notifyNotPromptOwner('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 403, body: {error: 'Not owner of prompt: p1'}});
    });
  });

  describe('When the prompt is already archived', () => {
    it('should build a 409 Conflict error view model', () => {
      // Arrange
      const presenter = new ArchivePromptApiPresenter();

      // Act
      presenter.notifyPromptAlreadyArchived('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 409, body: {error: 'Prompt already archived: p1'}});
    });
  });
});
