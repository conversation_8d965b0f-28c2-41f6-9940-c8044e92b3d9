import {EditPromptDraftApiPresenter} from '@src/server/PromptManagement/presentation/api/EditPromptDraft/EditPromptDraftApiPresenter';

describe('EditPromptDraftApiPresenter', () => {
  describe('When presenting an edited prompt draft', () => {
    it('should build a 200 view model with prompt id and content', () => {
      // Arrange
      const presenter = new EditPromptDraftApiPresenter();

      // Act
      presenter.presentPromptDraftEditedSuccessfully({promptId: 'p1', content: 'c'});

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 200, body: {promptId: 'p1', content: 'c'}});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should build a 404 error view model', () => {
      // Arrange
      const presenter = new EditPromptDraftApiPresenter();

      // Act
      presenter.notifyPromptNotFound('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 404, body: {error: 'Prompt not found: p1'}});
    });
  });

  describe('When the user is not the owner of the prompt', () => {
    it('should build a 403 Forbidden error view model', () => {
      // Arrange
      const presenter = new EditPromptDraftApiPresenter();

      // Act
      presenter.notifyNotPromptOwner('p1');

      // Assert
      expect(presenter.getViewModel()).toEqual({status: 403, body: {error: 'Not owner of prompt: p1'}});
    });
  });
});
