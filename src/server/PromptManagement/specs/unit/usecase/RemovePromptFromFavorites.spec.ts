import {
  RemovePromptFromFavorites
} from '@src/server/PromptManagement/application/usecase/RemovePromptFromFavorites/RemovePromptFromFavorites';
import {InMemoryPromptRepository} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeRemovePromptFromFavoritesPresenter} from "../../fakes/FakeRemovePromptFromFavoritesPresenter";
import {PromptNotFoundError} from '@src/server/PromptManagement/domain/Prompt/errors/PromptNotFoundError';
import {NotPromptOwnerError} from '@src/server/PromptManagement/domain/Prompt/errors/NotPromptOwnerError';
import {PromptNotInFavoriteError} from '@src/server/PromptManagement/domain/Prompt/errors/PromptNotInFavoriteError';

describe('Given a user who wants to remove a prompt from their favorites', () => {

  describe('When the user is the owner of the prompt and the prompt is in favorites', () => {
    it('should remove the prompt from the user\'s favorites', async () => {

      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('id', 'content', 'GPT-4', 'user');
      prompt.addToFavorites();
      await repository.save(prompt);
      const usecase = new RemovePromptFromFavorites(repository);
      const presenter = new FakeRemovePromptFromFavoritesPresenter();

      // Act
      await usecase.execute({promptId: 'id', userId: 'user'}, presenter);

      // Assert
      const saved = await repository.findById('id');
      expect(saved!.isInFavorite()).toBe(false);
      expect(presenter.response).toEqual({promptId: 'id', isFavorite: false});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should present PromptNotFoundError', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const usecase = new RemovePromptFromFavorites(repository);
      const presenter = new FakeRemovePromptFromFavoritesPresenter();

      // Act
      await usecase.execute({promptId: 'id', userId: 'user'}, presenter);

      // Assert
      expect(presenter.errorMessage).toEqual(new PromptNotFoundError('id').message);
      expect(await repository.findById('id')).toBeNull();
      expect(presenter.response).toBeNull();
    });
  });

  describe('When the user is not the owner', () => {
    it('should present NotPromptOwnerError', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('id', 'content', 'GPT-4', 'owner');
      prompt.addToFavorites();
      await repository.save(prompt);
      const usecase = new RemovePromptFromFavorites(repository);
      const presenter = new FakeRemovePromptFromFavoritesPresenter();

      // Act
      await usecase.execute({promptId: 'id', userId: 'other'}, presenter);

      // Assert
      expect(presenter.errorMessage).toEqual(new NotPromptOwnerError('id').message);
      const saved = await repository.findById('id');
      expect(saved!.isInFavorite()).toBe(true);
      expect(presenter.response).toBeNull();
    });
  });

  describe('When the prompt is not in favorites', () => {
    it('should present PromptNotInFavoriteError', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('id', 'content', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new RemovePromptFromFavorites(repository);
      const presenter = new FakeRemovePromptFromFavoritesPresenter();

      // Act
      await usecase.execute({promptId: 'id', userId: 'user'}, presenter);

      // Assert
      expect(presenter.errorMessage).toEqual(new PromptNotInFavoriteError('id').message);
      const saved = await repository.findById('id');
      expect(saved!.isInFavorite()).toBe(false);
      expect(presenter.response).toBeNull();
    });
  });

});
