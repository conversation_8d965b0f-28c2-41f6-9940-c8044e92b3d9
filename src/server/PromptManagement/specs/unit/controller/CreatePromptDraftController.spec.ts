import { CreatePromptDraftApiController } from '@src/server/PromptManagement/presentation/api/CreatePromptDraft/CreatePromptDraftApiController';
import { CreatePromptDraft } from '@src/server/PromptManagement/application/usecase/CreatePromptDraft/CreatePromptDraft';
import { InMemoryPromptRepository } from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import { FakeIdentityProvider } from '@src/server/Shared/infrastructure/IdentityProvider/FakeUUIDProvider';
import { FakeAuthenticationGateway } from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('CreatePromptDraftController', () => {
  describe('When creating a prompt draft', () => {
    it('should return 201 with prompt id', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const identityProvider = new FakeIdentityProvider();
      identityProvider.setNextIds('550e8400-e29b-41d4-a716-************');
      const usecase = new CreatePromptDraft(repository, identityProvider);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new CreatePromptDraftApiController(usecase, gateway);

      // Act
      const { status, body } = await controller.handle({content: 'c', targetAI: 'GPT-4'});

      // Assert
      expect(status).toBe(201);
      expect(body).toEqual({promptId: '550e8400-e29b-41d4-a716-************'});
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const identityProvider = new FakeIdentityProvider();
      identityProvider.setNextIds('550e8400-e29b-41d4-a716-************');
      const usecase = new CreatePromptDraft(repository, identityProvider);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new CreatePromptDraftApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({content: 'c', targetAI: 'GPT-4'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
