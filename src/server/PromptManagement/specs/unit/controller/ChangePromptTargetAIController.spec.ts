import {
  ChangePromptTargetAIApiController
} from '@src/server/PromptManagement/presentation/api/ChangePromptTarget/ChangePromptTargetAIApiController';
import {
  ChangePromptTargetAI
} from '@src/server/PromptManagement/application/usecase/ChangePromptTargetAI/ChangePromptTargetAI';
import {
  InMemoryPromptRepository
} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('ChangePromptTargetAIController', () => {
  describe('When changing target AI', () => {
    it('should return 200 with prompt id and target AI', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new ChangePromptTargetAI(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new ChangePromptTargetAIApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1', targetAI: 'GPT-3'});

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({promptId: 'p1', targetAI: 'GPT-3'});
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new ChangePromptTargetAI(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new ChangePromptTargetAIApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1', targetAI: 'GPT-3'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
