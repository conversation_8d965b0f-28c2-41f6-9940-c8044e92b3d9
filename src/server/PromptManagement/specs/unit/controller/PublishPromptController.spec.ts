import {PublishPromptApiController} from '@src/server/PromptManagement/presentation/api/PublishPrompt/PublishPromptApiController';
import {PublishPrompt} from '@src/server/PromptManagement/application/usecase/PublishPrompt/PublishPrompt';
import {InMemoryPromptRepository} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('PublishPromptController', () => {
  describe('When publishing a prompt', () => {
    it('should return 200 with prompt id and status', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new PublishPrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new PublishPromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({promptId: 'p1', status: 'published'});
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new PublishPrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new PublishPromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
