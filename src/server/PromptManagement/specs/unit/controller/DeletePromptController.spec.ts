import {DeletePromptApiController} from '@src/server/PromptManagement/presentation/api/DeletePrompt/DeletePromptApiController';
import {DeletePrompt} from '@src/server/PromptManagement/application/usecase/DeletePrompt/DeletePrompt';
import {InMemoryPromptRepository} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('DeletePromptController', () => {
  describe('When deleting a prompt', () => {
    it('should return 200 with prompt id', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new DeletePrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new DeletePromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({promptId: 'p1'});
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new DeletePrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new DeletePromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
