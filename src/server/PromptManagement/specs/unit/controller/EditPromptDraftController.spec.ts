import {EditPromptDraftApiController} from '@src/server/PromptManagement/presentation/api/EditPromptDraft/EditPromptDraftApiController';
import {EditPromptDraft} from '@src/server/PromptManagement/application/usecase/EditPromptDraft/EditPromptDraft';
import {InMemoryPromptRepository} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('EditPromptDraftController', () => {
  describe('When editing a prompt draft', () => {
    it('should return 200 with prompt id and content', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new EditPromptDraft(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new EditPromptDraftApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1', content: 'updated'});

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({promptId: 'p1', content: 'updated'});
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new EditPromptDraft(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new EditPromptDraftApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1', content: 'updated'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
