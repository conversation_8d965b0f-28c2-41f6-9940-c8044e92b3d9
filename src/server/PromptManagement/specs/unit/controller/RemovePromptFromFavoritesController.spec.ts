import {RemovePromptFromFavoritesApiController} from '@src/server/PromptManagement/presentation/api/RemovePromptFromFavorites/RemovePromptFromFavoritesApiController';
import {RemovePromptFromFavorites} from '@src/server/PromptManagement/application/usecase/RemovePromptFromFavorites/RemovePromptFromFavorites';
import {InMemoryPromptRepository} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('RemovePromptFromFavoritesController', () => {
  describe('When removing a prompt from favorites', () => {
    it('should return 200 with prompt id and favorite flag', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      prompt.addToFavorites();
      await repository.save(prompt);
      const usecase = new RemovePromptFromFavorites(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new RemovePromptFromFavoritesApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({promptId: 'p1', isFavorite: false});
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      prompt.addToFavorites();
      await repository.save(prompt);
      const usecase = new RemovePromptFromFavorites(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new RemovePromptFromFavoritesApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
