import {DuplicatePromptApiController} from '@src/server/PromptManagement/presentation/api/DuplicatePrompt/DuplicatePromptApiController';
import {DuplicatePrompt} from '@src/server/PromptManagement/application/usecase/DuplicatePrompt/DuplicatePrompt';
import {InMemoryPromptRepository} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeIdentityProvider} from '@src/server/Shared/infrastructure/IdentityProvider/FakeUUIDProvider';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('DuplicatePromptController', () => {
  describe('When duplicating a prompt', () => {
    it('should return 201 with new prompt id', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const identity = new FakeIdentityProvider();
      identity.setNextIds('550e8400-e29b-41d4-a716-************');
      const usecase = new DuplicatePrompt(repository, identity);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new DuplicatePromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(201);
      expect(body).toEqual({promptId: '550e8400-e29b-41d4-a716-************'});
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const identity = new FakeIdentityProvider();
      identity.setNextIds('550e8400-e29b-41d4-a716-************');
      const usecase = new DuplicatePrompt(repository, identity);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new DuplicatePromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
