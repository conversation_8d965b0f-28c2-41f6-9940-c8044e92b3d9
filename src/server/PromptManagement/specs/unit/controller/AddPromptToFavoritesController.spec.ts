import {
  AddPromptToFavoritesApiController
} from '@src/server/PromptManagement/presentation/api/AddPromptToFavorites/AddPromptToFavoritesApiController';
import {
  AddPromptToFavorites
} from '@src/server/PromptManagement/application/usecase/AddPromptToFavorites/AddPromptToFavorites';
import {
  InMemoryPromptRepository
} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('AddPromptToFavoritesController', () => {
  describe('When adding a prompt to favorites', () => {
    it('should return 200 with prompt id', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'content', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new AddPromptToFavorites(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new AddPromptToFavoritesApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({promptId: 'p1'});
    });
  });

  describe('When the prompt does not exist', () => {
    it('should return 404 error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const usecase = new AddPromptToFavorites(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new AddPromptToFavoritesApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(404);
      expect(body).toEqual({error: 'Prompt not found: p1'});
    });
  });

  describe('When the user is not the owner', () => {
    it('should return 403 error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'content', 'GPT-4', 'owner');
      await repository.save(prompt);
      const usecase = new AddPromptToFavorites(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new AddPromptToFavoritesApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(403);
      expect(body).toEqual({error: 'Not owner of prompt: p1'});
    });
  });

  describe('When the prompt is already in the user\'s favorites', () => {
    it('should return a 409 Conflict error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'content', 'GPT-4', 'user');
      prompt.addToFavorites();
      await repository.save(prompt);
      const usecase = new AddPromptToFavorites(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new AddPromptToFavoritesApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(409);
      expect(body).toEqual({error: 'Prompt already in favorites: p1'});
    });
  });

  describe('When user is not authenticated', () => {
    it('should return 401 error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'content', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new AddPromptToFavorites(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new AddPromptToFavoritesApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
