import {
  ArchivePromptApiController
} from '@src/server/PromptManagement/presentation/api/ArchivePrompt/ArchivePromptApiController';
import {ArchivePrompt} from '@src/server/PromptManagement/application/usecase/ArchivePrompt/ArchivePrompt';
import {
  InMemoryPromptRepository
} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('ArchivePromptController', () => {
  describe('When archiving a prompt', () => {
    it('should return 200 with prompt id and archived status', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new ArchivePrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new ArchivePromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({promptId: 'p1', isArchived: true});
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new ArchivePrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new ArchivePromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
