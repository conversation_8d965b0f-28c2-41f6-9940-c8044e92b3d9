import {
  AddTagToPromptApiController
} from '@src/server/PromptManagement/presentation/api/AddTagToPrompt/AddTagToPromptApiController';
import {AddTagToPrompt} from '@src/server/PromptManagement/application/usecase/AddTagToPrompt/AddTagToPrompt';
import {
  InMemoryPromptRepository
} from '@src/server/PromptManagement/infrastructure/PromptRepository/InMemoryPromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('AddTagToPromptController', () => {
  describe('When adding a tag to a prompt', () => {
    it('should return 200 with prompt id and tag', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new AddTagToPrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new AddTagToPromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1', tag: 'test'});

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({promptId: 'p1', tag: 'test'});
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptRepository();
      const prompt = Prompt.createDraft('p1', 'c', 'GPT-4', 'user');
      await repository.save(prompt);
      const usecase = new AddTagToPrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new AddTagToPromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1', tag: 'test'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
