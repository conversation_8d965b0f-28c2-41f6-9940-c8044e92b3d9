import {App} from 'hono-api/src/App';
import type {PromptRepository} from '@src/server/PromptManagement/application/port/driven/PromptRepository';
import {Prompt} from '@src/server/PromptManagement/domain/Prompt/Prompt';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('When calling POST /prompts/:id/publish', () => {
  it('should publish the prompt', async () => {
    // Arrange
    const app = new App();
    const repository = app.container.get<PromptRepository>('PromptRepository');
    const gateway = app.container.get<FakeAuthenticationGateway>('AuthenticationGateway');
    gateway.setSession('token-user', 'user');
    const prompt = Prompt.createDraft('p1', 'content', 'GPT-4', 'user');
    await repository.save(prompt);

    // Act
    const res = await app.request('/prompts/p1/publish', {
      method: 'POST',
      body: JSON.stringify({}),
      headers: {'Content-Type': 'application/json', Authorization: 'token-user'}
    });

    // Assert
    expect(res.status).toBe(200);
    expect(await res.json()).toEqual({promptId: 'p1', status: 'published'});
  });
});
