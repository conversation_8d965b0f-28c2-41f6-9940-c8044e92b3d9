import { App } from "hono-api/src/App";
import type { PromptRepository } from "@src/server/PromptManagement/application/port/driven/PromptRepository";
import { FakeAuthenticationGateway } from "@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway";

describe('When calling POST /prompts', () => {
  it('should create a prompt draft', async () => {
    // Arrange
    const app = new App();
    const repository = app.container.get<PromptRepository>('PromptRepository');
    const gateway = app.container.get<FakeAuthenticationGateway>('AuthenticationGateway');
    gateway.setSession('token-user', 'user');

    // Act
    const res = await app.request('/prompts', {
      method: 'POST',
      body: JSON.stringify({content: 'c', targetAI: 'GPT-4'}),
      headers: {'Content-Type': 'application/json', Authorization: 'token-user'}
    });

    // Assert
    expect(res.status).toBe(201);
    const body = await res.json();
    const saved = await repository.findById(body.promptId);
    expect(saved?.toSnapshot().content).toBe('c');
  });
});
