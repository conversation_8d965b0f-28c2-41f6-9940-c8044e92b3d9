import {createModule} from '@evyweb/ioctopus';
import {InMemoryPromptRepository} from './infrastructure/PromptRepository/InMemoryPromptRepository';
import {AddPromptToFavorites} from './application/usecase/AddPromptToFavorites/AddPromptToFavorites';
import {RemovePromptFromFavorites} from './application/usecase/RemovePromptFromFavorites/RemovePromptFromFavorites';
import {AddTagToPrompt} from './application/usecase/AddTagToPrompt/AddTagToPrompt';
import {RemoveTagFromPrompt} from './application/usecase/RemoveTagFromPrompt/RemoveTagFromPrompt';
import {CreatePromptDraft} from './application/usecase/CreatePromptDraft/CreatePromptDraft';
import {EditPromptDraft} from './application/usecase/EditPromptDraft/EditPromptDraft';
import {PublishPrompt} from './application/usecase/PublishPrompt/PublishPrompt';
import {DuplicatePrompt} from './application/usecase/DuplicatePrompt/DuplicatePrompt';
import {DeletePrompt} from './application/usecase/DeletePrompt/DeletePrompt';
import {ArchivePrompt} from './application/usecase/ArchivePrompt/ArchivePrompt';
import {RestorePrompt} from './application/usecase/RestorePrompt/RestorePrompt';
import {ChangePromptTargetAI} from './application/usecase/ChangePromptTargetAI/ChangePromptTargetAI';
import {AddPromptToFavoritesApiController} from './presentation/api/AddPromptToFavorites/AddPromptToFavoritesApiController';
import {RemovePromptFromFavoritesApiController} from './presentation/api/RemovePromptFromFavorites/RemovePromptFromFavoritesApiController';
import {AddTagToPromptApiController} from './presentation/api/AddTagToPrompt/AddTagToPromptApiController';
import {RemoveTagFromPromptApiController} from './presentation/api/RemoveTagFromPrompt/RemoveTagFromPromptApiController';
import {CreatePromptDraftApiController} from './presentation/api/CreatePromptDraft/CreatePromptDraftApiController';
import {EditPromptDraftApiController} from './presentation/api/EditPromptDraft/EditPromptDraftApiController';
import {PublishPromptApiController} from './presentation/api/PublishPrompt/PublishPromptApiController';
import {DuplicatePromptApiController} from './presentation/api/DuplicatePrompt/DuplicatePromptApiController';
import {DeletePromptApiController} from './presentation/api/DeletePrompt/DeletePromptApiController';
import {ArchivePromptApiController} from './presentation/api/ArchivePrompt/ArchivePromptApiController';
import {RestorePromptApiController} from './presentation/api/RestorePrompt/RestorePromptApiController';
import {ChangePromptTargetAIApiController} from './presentation/api/ChangePromptTarget/ChangePromptTargetAIApiController';

const promptManagementModule = createModule();

promptManagementModule.bind('PromptRepository').toClass(InMemoryPromptRepository);

promptManagementModule
  .bind('AddPromptToFavoritesUseCase')
  .toClass(AddPromptToFavorites, ['PromptRepository']);
promptManagementModule
  .bind('RemovePromptFromFavoritesUseCase')
  .toClass(RemovePromptFromFavorites, ['PromptRepository']);
promptManagementModule
  .bind('AddTagToPromptUseCase')
  .toClass(AddTagToPrompt, ['PromptRepository']);
promptManagementModule
  .bind('RemoveTagFromPromptUseCase')
  .toClass(RemoveTagFromPrompt, ['PromptRepository']);
promptManagementModule
  .bind('CreatePromptDraftUseCase')
  .toClass(CreatePromptDraft, ['PromptRepository', 'IdentityProvider']);
promptManagementModule
  .bind('EditPromptDraftUseCase')
  .toClass(EditPromptDraft, ['PromptRepository']);
promptManagementModule
  .bind('PublishPromptUseCase')
  .toClass(PublishPrompt, ['PromptRepository']);
promptManagementModule
  .bind('DuplicatePromptUseCase')
  .toClass(DuplicatePrompt, ['PromptRepository', 'IdentityProvider']);
promptManagementModule
  .bind('DeletePromptUseCase')
  .toClass(DeletePrompt, ['PromptRepository']);
promptManagementModule
  .bind('ArchivePromptUseCase')
  .toClass(ArchivePrompt, ['PromptRepository']);
promptManagementModule
  .bind('RestorePromptUseCase')
  .toClass(RestorePrompt, ['PromptRepository']);
promptManagementModule
  .bind('ChangePromptTargetAIUseCase')
  .toClass(ChangePromptTargetAI, ['PromptRepository']);

promptManagementModule
  .bind('AddPromptToFavoritesController')
  .toClass(AddPromptToFavoritesApiController, [
    'AddPromptToFavoritesUseCase',
    'AuthenticationGateway',
  ]);
promptManagementModule
  .bind('RemovePromptFromFavoritesController')
  .toClass(RemovePromptFromFavoritesApiController, [
    'RemovePromptFromFavoritesUseCase',
    'AuthenticationGateway',
  ]);
promptManagementModule
  .bind('AddTagToPromptController')
  .toClass(AddTagToPromptApiController, ['AddTagToPromptUseCase', 'AuthenticationGateway']);
promptManagementModule
  .bind('RemoveTagFromPromptController')
  .toClass(RemoveTagFromPromptApiController, [
    'RemoveTagFromPromptUseCase',
    'AuthenticationGateway',
  ]);
promptManagementModule
  .bind('CreatePromptDraftController')
  .toClass(CreatePromptDraftApiController, [
    'CreatePromptDraftUseCase',
    'AuthenticationGateway',
  ]);
promptManagementModule
  .bind('EditPromptDraftController')
  .toClass(EditPromptDraftApiController, ['EditPromptDraftUseCase', 'AuthenticationGateway']);
promptManagementModule
  .bind('PublishPromptController')
  .toClass(PublishPromptApiController, ['PublishPromptUseCase', 'AuthenticationGateway']);
promptManagementModule
  .bind('DuplicatePromptController')
  .toClass(DuplicatePromptApiController, ['DuplicatePromptUseCase', 'AuthenticationGateway']);
promptManagementModule
  .bind('DeletePromptController')
  .toClass(DeletePromptApiController, ['DeletePromptUseCase', 'AuthenticationGateway']);
promptManagementModule
  .bind('ArchivePromptController')
  .toClass(ArchivePromptApiController, ['ArchivePromptUseCase', 'AuthenticationGateway']);
promptManagementModule
  .bind('RestorePromptController')
  .toClass(RestorePromptApiController, ['RestorePromptUseCase', 'AuthenticationGateway']);
promptManagementModule
  .bind('ChangePromptTargetAIController')
  .toClass(ChangePromptTargetAIApiController, [
    'ChangePromptTargetAIUseCase',
    'AuthenticationGateway',
  ]);

export {promptManagementModule};
