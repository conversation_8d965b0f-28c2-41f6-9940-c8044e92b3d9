import {PromptAccessRepository} from '../../application/port/driven/PromptAccessRepository';
import {PromptAccess} from '../../domain/PromptAccess/PromptAccess';

export class InMemoryPromptAccessRepository implements PromptAccessRepository {
  private readonly accesses = new Map<string, PromptAccess>();

  async findById(promptId: string): Promise<PromptAccess | null> {
    return this.accesses.get(promptId) ?? null;
  }

  async save(access: PromptAccess): Promise<void> {
    this.accesses.set(access.getPromptId(), access);
  }
}
