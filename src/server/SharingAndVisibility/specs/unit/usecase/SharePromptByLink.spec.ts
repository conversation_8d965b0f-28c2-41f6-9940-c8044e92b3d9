import {SharePromptByLink} from '@src/server/SharingAndVisibility/application/usecase/SharePromptByLink/SharePromptByLink';
import {InMemoryPromptAccessRepository} from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import {FakeIdentityProvider} from '@src/server/Shared/infrastructure/IdentityProvider/FakeUUIDProvider';
import {FakeSharePromptByLinkPresenter} from '@src/server/SharingAndVisibility/specs/fakes/FakeSharePromptByLinkPresenter';
import {PromptAccess} from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';

describe('SharePromptByLink', () => {

  describe('When sharing a prompt by link', () => {
    it('should share the prompt via a link', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const identityProvider = new FakeIdentityProvider();
      identityProvider.setNextIds('550e8400-e29b-41d4-a716-************');
      const usecase = new SharePromptByLink(repository, identityProvider);
      const presenter = new FakeSharePromptByLinkPresenter();

      // Act
      await usecase.execute({promptId: 'p1', userId: 'user'}, presenter);

      // Assert
      const access = await repository.findById('p1');
      expect(access?.toSnapshot()).toEqual({
        promptId: 'p1',
        visibility: 'link',
        shareLink: '550e8400-e29b-41d4-a716-************',
        userIds: [],
      });
      expect(presenter.response).toEqual({promptId: 'p1', shareLink: '550e8400-e29b-41d4-a716-************'});
    });

  });

  describe('When a share link already exists for the prompt', () => {
    it('should regenerate a new share link for the prompt', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const identityProvider = new FakeIdentityProvider();
      identityProvider.setNextIds('550e8400-e29b-41d4-a716-446655440001');
      const existingAccess = PromptAccess.create('p1');
      existingAccess.setVisibility('link');
      existingAccess.setShareLink('old');
      await repository.save(existingAccess);
      const usecase = new SharePromptByLink(repository, identityProvider);
      const presenter = new FakeSharePromptByLinkPresenter();

      // Act
      await usecase.execute({promptId: 'p1', userId: 'user'}, presenter);

      // Assert
      const access = await repository.findById('p1');
      expect(access?.toSnapshot().shareLink).toBe('550e8400-e29b-41d4-a716-446655440001');
      expect(presenter.response).toEqual({promptId: 'p1', shareLink: '550e8400-e29b-41d4-a716-446655440001'});
    });
  });

});
