import {
  RemoveUserAccessFromPrompt
} from '@src/server/SharingAndVisibility/application/usecase/RemoveUserAccessFromPrompt/RemoveUserAccessFromPrompt';
import {
  InMemoryPromptAccessRepository
} from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import {PromptAccess} from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';
import {
  FakeRemoveUserAccessFromPromptPresenter
} from '@src/server/SharingAndVisibility/specs/fakes/FakeRemoveUserAccessFromPromptPresenter';

describe(`Given a user who wants to remove another user's access to a prompt`, () => {

  describe(`When the prompt exists and the target user has access`, () => {
    it(`should remove the target user from the prompt's access list`, async () => {

      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const access = PromptAccess.create('p1');
      access.shareWithUsers(['u1', 'u2']);
      await repository.save(access);
      const usecase = new RemoveUserAccessFromPrompt(repository);
      const presenter = new FakeRemoveUserAccessFromPromptPresenter();

      // Act
      await usecase.execute({promptId: 'p1', targetUserId: 'u1', userId: 'user'}, presenter);

      // Assert
      const saved = await repository.findById('p1');
      expect(saved?.toSnapshot().userIds).toEqual(['u2']);
      expect(presenter.response).toEqual({promptId: 'p1', userIds: ['u2']});
    });

  });

  describe(`When access does not exist`, () => {
    it(`should ignore unknown prompt`, async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const usecase = new RemoveUserAccessFromPrompt(repository);
      const presenter = new FakeRemoveUserAccessFromPromptPresenter();

      // Act
      await usecase.execute({promptId: 'p2', targetUserId: 'u1', userId: 'user'}, presenter);

      // Assert
      expect(await repository.findById('p2')).toBeNull();
      expect(presenter.response).toBeNull();
    });

    it(`should empty the access list when removing the last user`, async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const access = PromptAccess.create('p1');
      access.shareWithUsers(['u1']);
      await repository.save(access);
      const usecase = new RemoveUserAccessFromPrompt(repository);
      const presenter = new FakeRemoveUserAccessFromPromptPresenter();

      // Act
      await usecase.execute({promptId: 'p1', targetUserId: 'u1', userId: 'user'}, presenter);

      // Assert
      const saved = await repository.findById('p1');
      expect(saved?.toSnapshot().userIds).toEqual([]);
      expect(presenter.response).toEqual({promptId: 'p1', userIds: []});
    });

    describe(`When the target user does not have access to the prompt`, () => {
      it(`should not modify the access list`, async () => {
        // Arrange
        const repository = new InMemoryPromptAccessRepository();
        const access = PromptAccess.create('p1');
        access.shareWithUsers(['u1']);
        await repository.save(access);
        const usecase = new RemoveUserAccessFromPrompt(repository);
        const presenter = new FakeRemoveUserAccessFromPromptPresenter();

        // Act
        await usecase.execute({promptId: 'p1', targetUserId: 'u2', userId: 'user'}, presenter);

        // Assert
        const saved = await repository.findById('p1');
        expect(saved?.toSnapshot().userIds).toEqual(['u1']);
        expect(presenter.response).toEqual({promptId: 'p1', userIds: ['u1']});
      });
    });
  });

});