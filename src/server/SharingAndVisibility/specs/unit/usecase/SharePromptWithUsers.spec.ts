import {SharePromptWithUsers} from '@src/server/SharingAndVisibility/application/usecase/SharePromptWithUsers/SharePromptWithUsers';
import {InMemoryPromptAccessRepository} from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import {FakeSharePromptWithUsersPresenter} from '../../fakes/FakeSharePromptWithUsersPresenter';
import {PromptAccess} from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';

describe('SharePromptWithUsers', () => {

  describe('When sharing a new prompt', () => {
    it('should grant access to those users', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const usecase = new SharePromptWithUsers(repository);
      const presenter = new FakeSharePromptWithUsersPresenter();

      // Act
      await usecase.execute({promptId: 'p1', userIds: ['u1', 'u2'], userId: 'user'}, presenter);

      // Assert
      const access = await repository.findById('p1');
      expect(access?.toSnapshot()).toEqual({
        promptId: 'p1',
        visibility: 'private',
        shareLink: null,
        userIds: ['u1', 'u2'],
      });
      expect(presenter.response).toEqual({promptId: 'p1', userIds: ['u1', 'u2']});
    });

  });

  describe('When the prompt already has users', () => {
    it('should keep existing users and add new ones', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const existing = PromptAccess.create('p1');
      existing.shareWithUsers(['u1']);
      await repository.save(existing);
      const usecase = new SharePromptWithUsers(repository);
      const presenter = new FakeSharePromptWithUsersPresenter();

      // Act
      await usecase.execute({promptId: 'p1', userIds: ['u2'], userId: 'user'}, presenter);

      // Assert
      const access = await repository.findById('p1');
      expect(access?.toSnapshot().userIds).toEqual(['u1', 'u2']);
      expect(presenter.response).toEqual({promptId: 'p1', userIds: ['u1', 'u2']});
    });
  });

  

});
