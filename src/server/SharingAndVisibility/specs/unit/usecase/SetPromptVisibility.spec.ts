import {SetPromptVisibility} from '@src/server/SharingAndVisibility/application/usecase/SetPromptVisibility/SetPromptVisibility';
import {InMemoryPromptAccessRepository} from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import {FakeSetPromptVisibilityPresenter} from '@src/server/SharingAndVisibility/specs/fakes/FakeSetPromptVisibilityPresenter';
import {PromptAccess} from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';

describe('SetPromptVisibility', () => {

  describe('When setting a prompt visibility', () => {
    it('should set the prompt visibility', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const usecase = new SetPromptVisibility(repository);
      const presenter = new FakeSetPromptVisibilityPresenter();

      // Act
      await usecase.execute({promptId: 'p1', visibility: 'public', userId: 'user'}, presenter);

      // Assert
      const access = await repository.findById('p1');
      expect(access?.toSnapshot().visibility).toBe('public');
      expect(presenter.response).toEqual({promptId: 'p1', visibility: 'public'});
    });

  });

  describe('When updating the visibility of an existing prompt', () => {
    it('should update the prompt visibility', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const existing = PromptAccess.create('p1');
      await repository.save(existing);
      const usecase = new SetPromptVisibility(repository);
      const presenter = new FakeSetPromptVisibilityPresenter();

      // Act
      await usecase.execute({promptId: 'p1', visibility: 'private', userId: 'user'}, presenter);

      // Assert
      const access = await repository.findById('p1');
      expect(access?.toSnapshot().visibility).toBe('private');
      expect(presenter.response).toEqual({promptId: 'p1', visibility: 'private'});
    });
  });

});
