import {ViewPromptAccessList} from '@src/server/SharingAndVisibility/application/usecase/ViewPromptAccessList/ViewPromptAccessList';
import {InMemoryPromptAccessRepository} from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import {PromptAccess} from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';
import {FakeViewPromptAccessListPresenter} from '@src/server/SharingAndVisibility/specs/fakes/FakeViewPromptAccessListPresenter';

describe('Given a user who wants to view the access list for a prompt', () => {

  describe('When the prompt has existing access settings', () => {
    it('should display the prompt\'s access settings', async () => {

      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const access = PromptAccess.create('p1');
      access.setVisibility('link');
      access.setShareLink('link-123');
      access.shareWithUsers(['u1']);
      await repository.save(access);
      const usecase = new ViewPromptAccessList(repository);
      const presenter = new FakeViewPromptAccessListPresenter();

      // Act
      await usecase.execute({promptId: 'p1', userId: 'user'}, presenter);

      // Assert
      expect(presenter.response).toEqual({
        promptId: 'p1',
        visibility: 'link',
        userIds: ['u1'],
        shareLink: 'link-123',
      });
    });

  });

  describe('When no access exists yet', () => {
    it('should create default access settings', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const usecase = new ViewPromptAccessList(repository);
      const presenter = new FakeViewPromptAccessListPresenter();

      // Act
      await usecase.execute({promptId: 'p2', userId: 'user'}, presenter);

      // Assert
      expect(presenter.response).toEqual({promptId: 'p2', visibility: 'private', userIds: [], shareLink: null});
    });
  });

});
