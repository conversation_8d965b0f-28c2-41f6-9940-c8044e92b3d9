import { SharePromptByLinkApiController } from '@src/server/SharingAndVisibility/presentation/api/SharePromptByLink/SharePromptByLinkApiController';
import { SharePromptByLink } from '@src/server/SharingAndVisibility/application/usecase/SharePromptByLink/SharePromptByLink';
import { InMemoryPromptAccessRepository } from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import { FakeIdentityProvider } from '@src/server/Shared/infrastructure/IdentityProvider/FakeUUIDProvider';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('SharePromptByLinkController', () => {
  describe('When sharing a prompt by link', () => {
    it('should return 200 with share link', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const identityProvider = new FakeIdentityProvider();
      identityProvider.setNextIds('550e8400-e29b-41d4-a716-************');
      const usecase = new SharePromptByLink(repository, identityProvider);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new SharePromptByLinkApiController(usecase, gateway);

      // Act
      const { status, body } = await controller.handle({ promptId: 'p1' });

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({ promptId: 'p1', shareLink: '550e8400-e29b-41d4-a716-************' });
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const identityProvider = new FakeIdentityProvider();
      const usecase = new SharePromptByLink(repository, identityProvider);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new SharePromptByLinkApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
