import { ViewPromptAccessListApiController } from '@src/server/SharingAndVisibility/presentation/api/ViewPromptAccessList/ViewPromptAccessListApiController';
import { ViewPromptAccessList } from '@src/server/SharingAndVisibility/application/usecase/ViewPromptAccessList/ViewPromptAccessList';
import { InMemoryPromptAccessRepository } from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import { PromptAccess } from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('ViewPromptAccessListController', () => {
  describe('When viewing prompt access list', () => {
    it('should return 200 with access info', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const access = PromptAccess.create('p1');
      access.shareWithUsers(['u1']);
      await repository.save(access);
      const usecase = new ViewPromptAccessList(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new ViewPromptAccessListApiController(usecase, gateway);

      // Act
      const { status, body } = await controller.handle({ promptId: 'p1' });

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({ promptId: 'p1', visibility: 'private', userIds: ['u1'], shareLink: null });
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const usecase = new ViewPromptAccessList(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new ViewPromptAccessListApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
