import { SetPromptVisibilityApiController } from '@src/server/SharingAndVisibility/presentation/api/SetPromptVisibility/SetPromptVisibilityApiController';
import { SetPromptVisibility } from '@src/server/SharingAndVisibility/application/usecase/SetPromptVisibility/SetPromptVisibility';
import { InMemoryPromptAccessRepository } from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('SetPromptVisibilityController', () => {
  describe('When setting prompt visibility', () => {
    it('should return 200 with visibility', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const usecase = new SetPromptVisibility(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new SetPromptVisibilityApiController(usecase, gateway);

      // Act
      const { status, body } = await controller.handle({ promptId: 'p1', visibility: 'public' });

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({ promptId: 'p1', visibility: 'public' });
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const usecase = new SetPromptVisibility(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new SetPromptVisibilityApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1', visibility: 'public'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
