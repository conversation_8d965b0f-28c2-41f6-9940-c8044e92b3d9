import { SharePromptWithUsersApiController } from '@src/server/SharingAndVisibility/presentation/api/SharePromptWithUsers/SharePromptWithUsersApiController';
import { SharePromptWithUsers } from '@src/server/SharingAndVisibility/application/usecase/SharePromptWithUsers/SharePromptWithUsers';
import { InMemoryPromptAccessRepository } from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe('SharePromptWithUsersController', () => {
  describe('When sharing a prompt with users', () => {
    it('should return 200 with user ids', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const usecase = new SharePromptWithUsers(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new SharePromptWithUsersApiController(usecase, gateway);

      // Act
      const { status, body } = await controller.handle({ promptId: 'p1', userIds: ['u1', 'u2'] });

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({ promptId: 'p1', userIds: ['u1', 'u2'] });
    });
  });

  describe('When the user is not authenticated', () => {
    it('should return a 401 Unauthorized error', async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const usecase = new SharePromptWithUsers(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new SharePromptWithUsersApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1', userIds: ['u1']});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
