import { RemoveUserAccessFromPromptApiController } from '@src/server/SharingAndVisibility/presentation/api/RemoveUserAccessFromPrompt/RemoveUserAccessFromPromptApiController';
import { RemoveUserAccessFromPrompt } from '@src/server/SharingAndVisibility/application/usecase/RemoveUserAccessFromPrompt/RemoveUserAccessFromPrompt';
import { InMemoryPromptAccessRepository } from '@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository';
import { PromptAccess } from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';
import {FakeAuthenticationGateway} from '@src/server/Authentication/infrastructure/AuthenticationGateway/FakeAuthenticationGateway';

describe(`Given a user who wants to remove another user's access to a prompt via the API`, () => {

  describe(`When the user is authenticated and the access is successfully removed`, () => {
    it(`should return a 200 OK status with the remaining user IDs`, async () => {

      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const access = PromptAccess.create('p1');
      access.shareWithUsers(['u1', 'u2']);
      await repository.save(access);
      const usecase = new RemoveUserAccessFromPrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId('user');
      const controller = new RemoveUserAccessFromPromptApiController(usecase, gateway);

      // Act
      const { status, body } = await controller.handle({ promptId: 'p1', targetUserId: 'u1' });

      // Assert
      expect(status).toBe(200);
      expect(body).toEqual({ promptId: 'p1', userIds: ['u2'] });
    });
  });

  describe(`When user is not authenticated`, () => {
    it(`should return 401 error`, async () => {
      // Arrange
      const repository = new InMemoryPromptAccessRepository();
      const access = PromptAccess.create('p1');
      access.shareWithUsers(['u1']);
      await repository.save(access);
      const usecase = new RemoveUserAccessFromPrompt(repository);
      const gateway = new FakeAuthenticationGateway();
      gateway.setCurrentUserId(null);
      const controller = new RemoveUserAccessFromPromptApiController(usecase, gateway);

      // Act
      const {status, body} = await controller.handle({promptId: 'p1', targetUserId: 'u1'});

      // Assert
      expect(status).toBe(401);
      expect(body).toEqual({error: 'Not authenticated'});
    });
  });
});
