import {SetPromptVisibilityPresenter} from '@src/server/SharingAndVisibility/application/port/driven/SetPromptVisibilityPresenter';
import {SetPromptVisibilityResponse} from '@src/server/SharingAndVisibility/application/usecase/SetPromptVisibility/SetPromptVisibilityResponse';

export class FakeSetPromptVisibilityPresenter implements SetPromptVisibilityPresenter {
  response: SetPromptVisibilityResponse | null = null;
  isUserNotAuthenticated = false;

  displayPromptVisibilitySet(response: SetPromptVisibilityResponse): void {
    this.response = response;
  }

  notifyUserNotAuthenticated(): void {
    this.isUserNotAuthenticated = true;
  }
}
