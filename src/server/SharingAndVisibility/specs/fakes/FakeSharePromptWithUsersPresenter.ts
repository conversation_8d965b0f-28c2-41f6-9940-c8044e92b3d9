import {SharePromptWithUsersPresenter} from '@src/server/SharingAndVisibility/application/port/driven/SharePromptWithUsersPresenter';
import {SharePromptWithUsersResponse} from '@src/server/SharingAndVisibility/application/usecase/SharePromptWithUsers/SharePromptWithUsersResponse';

export class FakeSharePromptWithUsersPresenter implements SharePromptWithUsersPresenter {
  response: SharePromptWithUsersResponse | null = null;
  isUserNotAuthenticated = false;

  displayPromptSharedWithUsers(response: SharePromptWithUsersResponse): void {
    this.response = response;
  }

  notifyUserNotAuthenticated(): void {
    this.isUserNotAuthenticated = true;
  }
}
