import {RemoveUserAccessFromPromptPresenter} from '@src/server/SharingAndVisibility/application/port/driven/RemoveUserAccessFromPromptPresenter';
import {RemoveUserAccessFromPromptResponse} from '@src/server/SharingAndVisibility/application/usecase/RemoveUserAccessFromPrompt/RemoveUserAccessFromPromptResponse';

export class FakeRemoveUserAccessFromPromptPresenter implements RemoveUserAccessFromPromptPresenter {
  response: RemoveUserAccessFromPromptResponse | null = null;
  isUserNotAuthenticated = false;

  displayUserAccessRemoved(response: RemoveUserAccessFromPromptResponse): void {
    this.response = response;
  }

  notifyUserNotAuthenticated(): void {
    this.isUserNotAuthenticated = true;
  }
}
