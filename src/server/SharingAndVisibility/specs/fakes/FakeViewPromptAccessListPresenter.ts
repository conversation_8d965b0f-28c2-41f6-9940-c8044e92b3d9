import {ViewPromptAccessListPresenter} from '@src/server/SharingAndVisibility/application/port/driven/ViewPromptAccessListPresenter';
import {ViewPromptAccessListResponse} from '@src/server/SharingAndVisibility/application/usecase/ViewPromptAccessList/ViewPromptAccessListResponse';

export class FakeViewPromptAccessListPresenter implements ViewPromptAccessListPresenter {
  response: ViewPromptAccessListResponse | null = null;
  isUserNotAuthenticated = false;

  displayPromptAccessList(response: ViewPromptAccessListResponse): void {
    this.response = response;
  }

  notifyUserNotAuthenticated(): void {
    this.isUserNotAuthenticated = true;
  }
}
