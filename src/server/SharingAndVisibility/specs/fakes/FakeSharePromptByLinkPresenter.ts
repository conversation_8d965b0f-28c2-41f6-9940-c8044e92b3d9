import {SharePromptByLinkPresenter} from '@src/server/SharingAndVisibility/application/port/driven/SharePromptByLinkPresenter';
import {SharePromptByLinkResponse} from '@src/server/SharingAndVisibility/application/usecase/SharePromptByLink/SharePromptByLinkResponse';

export class FakeSharePromptByLinkPresenter implements SharePromptByLinkPresenter {
  response: SharePromptByLinkResponse | null = null;
  isUserNotAuthenticated = false;

  displayPromptSharedByLink(response: SharePromptByLinkResponse): void {
    this.response = response;
  }

  notifyUserNotAuthenticated(): void {
    this.isUserNotAuthenticated = true;
  }
}
