import {createModule} from "@evyweb/ioctopus";
import {
  InMemoryPromptAccessRepository
} from "@src/server/SharingAndVisibility/infrastructure/PromptAccessRepository/InMemoryPromptAccessRepository";
import {
  RemoveUserAccessFromPromptApiPresenter
} from "@src/server/SharingAndVisibility/presentation/api/RemoveUserAccessFromPrompt/RemoveUserAccessFromPromptApiPresenter";
import {
  SharePromptByLinkApiPresenter
} from "@src/server/SharingAndVisibility/presentation/api/SharePromptByLink/SharePromptByLinkApiPresenter";
import {
  SharePromptWithUsersApiPresenter
} from "@src/server/SharingAndVisibility/presentation/api/SharePromptWithUsers/SharePromptWithUsersApiPresenter";
import {
  SetPromptVisibilityApiPresenter
} from "@src/server/SharingAndVisibility/presentation/api/SetPromptVisibility/SetPromptVisibilityApiPresenter";
import {
  ViewPromptAccessListApiPresenter
} from "@src/server/SharingAndVisibility/presentation/api/ViewPromptAccessList/ViewPromptAccessListApiPresenter";
import {
  RemoveUserAccessFromPrompt
} from "@src/server/SharingAndVisibility/application/usecase/RemoveUserAccessFromPrompt/RemoveUserAccessFromPrompt";
import {
  SharePromptByLink
} from "@src/server/SharingAndVisibility/application/usecase/SharePromptByLink/SharePromptByLink";
import {
  SharePromptWithUsers
} from "@src/server/SharingAndVisibility/application/usecase/SharePromptWithUsers/SharePromptWithUsers";
import {
  SetPromptVisibility
} from "@src/server/SharingAndVisibility/application/usecase/SetPromptVisibility/SetPromptVisibility";
import {
  ViewPromptAccessList
} from "@src/server/SharingAndVisibility/application/usecase/ViewPromptAccessList/ViewPromptAccessList";
import {
  SharePromptWithUsersApiController
} from "@src/server/SharingAndVisibility/presentation/api/SharePromptWithUsers/SharePromptWithUsersApiController";
import {
  RemoveUserAccessFromPromptApiController
} from "@src/server/SharingAndVisibility/presentation/api/RemoveUserAccessFromPrompt/RemoveUserAccessFromPromptApiController";
import {
  SharePromptByLinkApiController
} from "@src/server/SharingAndVisibility/presentation/api/SharePromptByLink/SharePromptByLinkApiController";
import {
  SetPromptVisibilityApiController
} from "@src/server/SharingAndVisibility/presentation/api/SetPromptVisibility/SetPromptVisibilityApiController";
import {
  ViewPromptAccessListApiController
} from "@src/server/SharingAndVisibility/presentation/api/ViewPromptAccessList/ViewPromptAccessListApiController";

const sharingAndVisibilityModule = createModule();

sharingAndVisibilityModule.bind('PromptAccessRepository').toClass(InMemoryPromptAccessRepository);

sharingAndVisibilityModule.bind('RemoveUserAccessFromPromptPresenter').toClass(RemoveUserAccessFromPromptApiPresenter);
sharingAndVisibilityModule.bind('SharePromptByLinkPresenter').toClass(SharePromptByLinkApiPresenter);
sharingAndVisibilityModule.bind('SharePromptWithUsersPresenter').toClass(SharePromptWithUsersApiPresenter);
sharingAndVisibilityModule.bind('SetPromptVisibilityPresenter').toClass(SetPromptVisibilityApiPresenter);
sharingAndVisibilityModule.bind('ViewPromptAccessListPresenter').toClass(ViewPromptAccessListApiPresenter);

sharingAndVisibilityModule.bind('RemoveUserAccessFromPromptUseCase').toClass(RemoveUserAccessFromPrompt, ['PromptAccessRepository']);
sharingAndVisibilityModule.bind('SharePromptByLinkUseCase').toClass(SharePromptByLink, ['PromptAccessRepository', 'IdentityProvider']);
sharingAndVisibilityModule.bind('SharePromptWithUsersUseCase').toClass(SharePromptWithUsers, ['PromptAccessRepository']);
sharingAndVisibilityModule.bind('SetPromptVisibilityUseCase').toClass(SetPromptVisibility, ['PromptAccessRepository']);
sharingAndVisibilityModule.bind('ViewPromptAccessListUseCase').toClass(ViewPromptAccessList, ['PromptAccessRepository']);

sharingAndVisibilityModule.bind('RemoveUserAccessFromPromptController').toClass(RemoveUserAccessFromPromptApiController, ['RemoveUserAccessFromPromptUseCase']);
sharingAndVisibilityModule.bind('SharePromptByLinkController').toClass(SharePromptByLinkApiController, ['SharePromptByLinkUseCase']);
sharingAndVisibilityModule.bind('SharePromptWithUsersController').toClass(SharePromptWithUsersApiController, ['SharePromptWithUsersUseCase']);
sharingAndVisibilityModule.bind('SetPromptVisibilityController').toClass(SetPromptVisibilityApiController, ['SetPromptVisibilityUseCase']);
sharingAndVisibilityModule
  .bind('ViewPromptAccessListController')
  .toClass(ViewPromptAccessListApiController, ['ViewPromptAccessListUseCase']);

export {sharingAndVisibilityModule};
