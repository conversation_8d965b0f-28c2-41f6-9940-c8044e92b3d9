import { SharePromptWithUsersPresenter } from '@src/server/SharingAndVisibility/application/port/driven/SharePromptWithUsersPresenter';
import { SharePromptWithUsersResponse } from '@src/server/SharingAndVisibility/application/usecase/SharePromptWithUsers/SharePromptWithUsersResponse';
import { SharePromptWithUsersApiViewModel } from './SharePromptWithUsersApiViewModel';

export class SharePromptWithUsersApiPresenter implements SharePromptWithUsersPresenter {
  private viewModel: SharePromptWithUsersApiViewModel | null = null;

  getViewModel(): SharePromptWithUsersApiViewModel {
    return this.viewModel!;
  }

  displayPromptSharedWithUsers(response: SharePromptWithUsersResponse): void {
    this.viewModel = { status: 200, body: { promptId: response.promptId, userIds: response.userIds } };
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = { status: 401, body: { error: 'Not authenticated' } };
  }
}
