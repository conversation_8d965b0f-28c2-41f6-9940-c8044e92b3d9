import { SharePromptWithUsersUseCase } from '@src/server/SharingAndVisibility/application/port/driving/SharePromptWithUsersUseCase';
import { SharePromptWithUsersApiPresenter } from './SharePromptWithUsersApiPresenter';
import { SharePromptWithUsersApiViewModel } from './SharePromptWithUsersApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {SharePromptWithUsersApiRequest} from './SharePromptWithUsersApiRequest';

export class SharePromptWithUsersApiController {
  private readonly usecase: SharePromptWithUsersUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: SharePromptWithUsersUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: SharePromptWithUsersApiRequest): Promise<SharePromptWithUsersApiViewModel> {
    const presenter = new SharePromptWithUsersApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({...request, userId}, presenter);
    return presenter.getViewModel();
  }
}
