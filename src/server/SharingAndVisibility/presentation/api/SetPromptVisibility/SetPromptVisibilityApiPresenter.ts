import { SetPromptVisibilityPresenter } from '@src/server/SharingAndVisibility/application/port/driven/SetPromptVisibilityPresenter';
import { SetPromptVisibilityResponse } from '@src/server/SharingAndVisibility/application/usecase/SetPromptVisibility/SetPromptVisibilityResponse';
import { SetPromptVisibilityApiViewModel } from './SetPromptVisibilityApiViewModel';

export class SetPromptVisibilityApiPresenter implements SetPromptVisibilityPresenter {
  private viewModel: SetPromptVisibilityApiViewModel | null = null;

  getViewModel(): SetPromptVisibilityApiViewModel {
    return this.viewModel!;
  }

  displayPromptVisibilitySet(response: SetPromptVisibilityResponse): void {
    this.viewModel = { status: 200, body: { promptId: response.promptId, visibility: response.visibility } };
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = { status: 401, body: { error: 'Not authenticated' } };
  }
}
