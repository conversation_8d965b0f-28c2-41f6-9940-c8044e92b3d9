import { SetPromptVisibilityUseCase } from '@src/server/SharingAndVisibility/application/port/driving/SetPromptVisibilityUseCase';
import { SetPromptVisibilityApiPresenter } from './SetPromptVisibilityApiPresenter';
import { SetPromptVisibilityApiViewModel } from './SetPromptVisibilityApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {SetPromptVisibilityApiRequest} from './SetPromptVisibilityApiRequest';

export class SetPromptVisibilityApiController {
  private readonly usecase: SetPromptVisibilityUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: SetPromptVisibilityUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: SetPromptVisibilityApiRequest): Promise<SetPromptVisibilityApiViewModel> {
    const presenter = new SetPromptVisibilityApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({...request, userId}, presenter);
    return presenter.getViewModel();
  }
}
