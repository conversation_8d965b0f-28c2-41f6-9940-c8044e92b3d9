import { RemoveUserAccessFromPromptUseCase } from '@src/server/SharingAndVisibility/application/port/driving/RemoveUserAccessFromPromptUseCase';
import { RemoveUserAccessFromPromptApiPresenter } from './RemoveUserAccessFromPromptApiPresenter';
import { RemoveUserAccessFromPromptApiViewModel } from './RemoveUserAccessFromPromptApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {RemoveUserAccessFromPromptApiRequest} from './RemoveUserAccessFromPromptApiRequest';

export class RemoveUserAccessFromPromptApiController {
  private readonly usecase: RemoveUserAccessFromPromptUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: RemoveUserAccessFromPromptUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: RemoveUserAccessFromPromptApiRequest): Promise<RemoveUserAccessFromPromptApiViewModel> {
    const presenter = new RemoveUserAccessFromPromptApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({...request, userId}, presenter);
    return presenter.getViewModel();
  }
}
