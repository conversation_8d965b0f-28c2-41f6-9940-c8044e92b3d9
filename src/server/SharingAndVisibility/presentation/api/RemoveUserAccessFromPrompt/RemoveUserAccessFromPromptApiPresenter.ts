import { RemoveUserAccessFromPromptPresenter } from '@src/server/SharingAndVisibility/application/port/driven/RemoveUserAccessFromPromptPresenter';
import { RemoveUserAccessFromPromptResponse } from '@src/server/SharingAndVisibility/application/usecase/RemoveUserAccessFromPrompt/RemoveUserAccessFromPromptResponse';
import { RemoveUserAccessFromPromptApiViewModel } from './RemoveUserAccessFromPromptApiViewModel';

export class RemoveUserAccessFromPromptApiPresenter implements RemoveUserAccessFromPromptPresenter {
  private viewModel: RemoveUserAccessFromPromptApiViewModel | null = null;

  getViewModel(): RemoveUserAccessFromPromptApiViewModel {
    return this.viewModel!;
  }

  displayUserAccessRemoved(response: RemoveUserAccessFromPromptResponse): void {
    this.viewModel = { status: 200, body: { promptId: response.promptId, userIds: response.userIds } };
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = { status: 401, body: { error: 'Not authenticated' } };
  }
}
