import { SharePromptByLinkPresenter } from '@src/server/SharingAndVisibility/application/port/driven/SharePromptByLinkPresenter';
import { SharePromptByLinkResponse } from '@src/server/SharingAndVisibility/application/usecase/SharePromptByLink/SharePromptByLinkResponse';
import { SharePromptByLinkApiViewModel } from './SharePromptByLinkApiViewModel';

export class SharePromptByLinkApiPresenter implements SharePromptByLinkPresenter {
  private viewModel: SharePromptByLinkApiViewModel | null = null;

  getViewModel(): SharePromptByLinkApiViewModel {
    return this.viewModel!;
  }

  displayPromptSharedByLink(response: SharePromptByLinkResponse): void {
    this.viewModel = { status: 200, body: { promptId: response.promptId, shareLink: response.shareLink } };
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = { status: 401, body: { error: 'Not authenticated' } };
  }
}
