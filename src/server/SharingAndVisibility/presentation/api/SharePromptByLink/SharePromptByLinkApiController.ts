import { SharePromptByLinkUseCase } from '@src/server/SharingAndVisibility/application/port/driving/SharePromptByLinkUseCase';
import { SharePromptByLinkApiPresenter } from './SharePromptByLinkApiPresenter';
import { SharePromptByLinkApiViewModel } from './SharePromptByLinkApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {SharePromptByLinkApiRequest} from './SharePromptByLinkApiRequest';

export class SharePromptByLinkApiController {
  private readonly usecase: SharePromptByLinkUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: SharePromptByLinkUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: SharePromptByLinkApiRequest): Promise<SharePromptByLinkApiViewModel> {
    const presenter = new SharePromptByLinkApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({...request, userId}, presenter);
    return presenter.getViewModel();
  }
}
