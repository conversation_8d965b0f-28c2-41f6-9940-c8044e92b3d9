import { ViewPromptAccessListPresenter } from '@src/server/SharingAndVisibility/application/port/driven/ViewPromptAccessListPresenter';
import { ViewPromptAccessListResponse } from '@src/server/SharingAndVisibility/application/usecase/ViewPromptAccessList/ViewPromptAccessListResponse';
import { ViewPromptAccessListApiViewModel } from './ViewPromptAccessListApiViewModel';

export class ViewPromptAccessListApiPresenter implements ViewPromptAccessListPresenter {
  private viewModel: ViewPromptAccessListApiViewModel | null = null;

  getViewModel(): ViewPromptAccessListApiViewModel {
    return this.viewModel!;
  }

  displayPromptAccessList(response: ViewPromptAccessListResponse): void {
    this.viewModel = { status: 200, body: { ...response } };
  }

  notifyUserNotAuthenticated(): void {
    this.viewModel = { status: 401, body: { error: 'Not authenticated' } };
  }
}
