import { ViewPromptAccessListUseCase } from '@src/server/SharingAndVisibility/application/port/driving/ViewPromptAccessListUseCase';
import { ViewPromptAccessListApiPresenter } from './ViewPromptAccessListApiPresenter';
import { ViewPromptAccessListApiViewModel } from './ViewPromptAccessListApiViewModel';
import {AuthenticationGateway} from '@src/server/Authentication';
import {ViewPromptAccessListApiRequest} from './ViewPromptAccessListApiRequest';

export class ViewPromptAccessListApiController {
  private readonly usecase: ViewPromptAccessListUseCase;
  private readonly authenticationGateway: AuthenticationGateway;

  constructor(usecase: ViewPromptAccessListUseCase, authenticationGateway: AuthenticationGateway) {
    this.usecase = usecase;
    this.authenticationGateway = authenticationGateway;
  }

  async handle(request: ViewPromptAccessListApiRequest): Promise<ViewPromptAccessListApiViewModel> {
    const presenter = new ViewPromptAccessListApiPresenter();
    const userId = this.authenticationGateway.getCurrentUserId();
    if (!userId) {
      presenter.notifyUserNotAuthenticated();
      return presenter.getViewModel();
    }
    await this.usecase.execute({...request, userId}, presenter);
    return presenter.getViewModel();
  }
}
