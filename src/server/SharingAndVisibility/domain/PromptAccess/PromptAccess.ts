import {PromptAccessSnapshot} from './PromptAccessSnapshot';

export class PromptAccess {
  private readonly promptId: string;
  private visibility: 'private' | 'public' | 'link';
  private shareLink: string | null;
  private userIds: string[];

  private constructor(snapshot: PromptAccessSnapshot) {
    this.promptId = snapshot.promptId;
    this.visibility = snapshot.visibility;
    this.shareLink = snapshot.shareLink;
    this.userIds = [...snapshot.userIds];
  }

  getPromptId(): string {
    return this.promptId;
  }

  static create(promptId: string): PromptAccess {
    return new PromptAccess({promptId, visibility: 'private', shareLink: null, userIds: []});
  }

  static fromSnapshot(snapshot: PromptAccessSnapshot): PromptAccess {
    return new PromptAccess(snapshot);
  }

  toSnapshot(): PromptAccessSnapshot {
    return {
      promptId: this.promptId,
      visibility: this.visibility,
      shareLink: this.shareLink,
      userIds: [...this.userIds],
    };
  }

  setVisibility(visibility: 'private' | 'public' | 'link'): void {
    this.visibility = visibility;
  }

  setShareLink(link: string): void {
    this.shareLink = link;
  }

  shareWithUsers(userIds: string[]): void {
    for (const id of userIds) {
      if (!this.userIds.includes(id)) {
        this.userIds.push(id);
      }
    }
  }

  removeUser(userId: string): void {
    this.userIds = this.userIds.filter((id) => id !== userId);
  }
}
