import {PromptAccessRepository} from '@src/server/SharingAndVisibility/application/port/driven/PromptAccessRepository';
import {
  SetPromptVisibilityRequest
} from '@src/server/SharingAndVisibility/application/usecase/SetPromptVisibility/SetPromptVisibilityRequest';
import { SetPromptVisibilityPresenter } from '@src/server/SharingAndVisibility/application/port/driven/SetPromptVisibilityPresenter';
import { SetPromptVisibilityUseCase } from '@src/server/SharingAndVisibility/application/port/driving/SetPromptVisibilityUseCase';
import {PromptAccess} from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';

export class SetPromptVisibility implements SetPromptVisibilityUseCase {
  private readonly repository: PromptAccessRepository;

  constructor(repository: PromptAccessRepository) {
    this.repository = repository;
  }

  async execute(request: SetPromptVisibilityRequest, presenter: SetPromptVisibilityPresenter): Promise<void> {
    const {promptId, visibility} = request;
    let access = await this.repository.findById(promptId);
    if (!access) {
      access = PromptAccess.create(promptId);
    }
    access.setVisibility(visibility);
    await this.repository.save(access);
    presenter.displayPromptVisibilitySet({promptId: access.getPromptId(), visibility});
  }
}
