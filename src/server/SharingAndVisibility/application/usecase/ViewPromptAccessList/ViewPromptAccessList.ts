import {PromptAccessRepository} from '@src/server/SharingAndVisibility/application/port/driven/PromptAccessRepository';
import {ViewPromptAccessListRequest} from '@src/server/SharingAndVisibility/application/usecase/ViewPromptAccessList/ViewPromptAccessListRequest';
import {ViewPromptAccessListPresenter} from '@src/server/SharingAndVisibility/application/port/driven/ViewPromptAccessListPresenter';
import {ViewPromptAccessListUseCase} from '@src/server/SharingAndVisibility/application/port/driving/ViewPromptAccessListUseCase';
import {PromptAccess} from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';

export class ViewPromptAccessList implements ViewPromptAccessListUseCase {
  private readonly repository: PromptAccessRepository;

  constructor(repository: PromptAccessRepository) {
    this.repository = repository;
  }

  async execute(request: ViewPromptAccessListRequest, presenter: ViewPromptAccessListPresenter): Promise<void> {
    const {promptId} = request;
    let access = await this.repository.findById(promptId);
    if (!access) {
      access = PromptAccess.create(promptId);
    }
    await this.repository.save(access);
    const snapshot = access.toSnapshot();
    presenter.displayPromptAccessList({
      promptId: snapshot.promptId,
      visibility: snapshot.visibility,
      userIds: snapshot.userIds,
      shareLink: snapshot.shareLink,
    });
  }
}
