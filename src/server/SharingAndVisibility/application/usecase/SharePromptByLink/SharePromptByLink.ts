import {PromptAccessRepository} from '@src/server/SharingAndVisibility/application/port/driven/PromptAccessRepository';
import {
  SharePromptByLinkRequest
} from '@src/server/SharingAndVisibility/application/usecase/SharePromptByLink/SharePromptByLinkRequest';
import {SharePromptByLinkPresenter} from '@src/server/SharingAndVisibility/application/port/driven/SharePromptByLinkPresenter';
import {SharePromptByLinkUseCase} from '@src/server/SharingAndVisibility/application/port/driving/SharePromptByLinkUseCase';
import {PromptAccess} from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';
import {IdentityProvider} from '@src/server/Shared/application/port/IdentityProvider';

export class SharePromptByLink implements SharePromptByLinkUseCase {
  private readonly repository: PromptAccessRepository;
  private readonly identityProvider: IdentityProvider;

  constructor(repository: PromptAccessRepository, identityProvider: IdentityProvider) {
    this.repository = repository;
    this.identityProvider = identityProvider;
  }

  async execute(request: SharePromptByLinkRequest, presenter: SharePromptByLinkPresenter): Promise<void> {
    const {promptId} = request;
    let access = await this.repository.findById(promptId);
    if (!access) {
      access = PromptAccess.create(promptId);
    }
    const link = this.identityProvider.generateId();
    access.setVisibility('link');
    access.setShareLink(link);
    await this.repository.save(access);
    presenter.displayPromptSharedByLink({promptId: access.getPromptId(), shareLink: link});
  }
}
