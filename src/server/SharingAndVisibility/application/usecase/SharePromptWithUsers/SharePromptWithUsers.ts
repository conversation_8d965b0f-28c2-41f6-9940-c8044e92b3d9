import {PromptAccessRepository} from '@src/server/SharingAndVisibility/application/port/driven/PromptAccessRepository';
import {
  SharePromptWithUsersRequest
} from '@src/server/SharingAndVisibility/application/usecase/SharePromptWithUsers/SharePromptWithUsersRequest';
import { SharePromptWithUsersPresenter } from '@src/server/SharingAndVisibility/application/port/driven/SharePromptWithUsersPresenter';
import { SharePromptWithUsersUseCase } from '@src/server/SharingAndVisibility/application/port/driving/SharePromptWithUsersUseCase';
import {PromptAccess} from '@src/server/SharingAndVisibility/domain/PromptAccess/PromptAccess';

export class SharePromptWithUsers implements SharePromptWithUsersUseCase {
  private readonly repository: PromptAccessRepository;

  constructor(repository: PromptAccessRepository) {
    this.repository = repository;
  }

  async execute(request: SharePromptWithUsersRequest, presenter: SharePromptWithUsersPresenter): Promise<void> {
    const {promptId, userIds} = request;
    let access = await this.repository.findById(promptId);
    if (!access) {
      access = PromptAccess.create(promptId);
    }
    access.shareWithUsers(userIds);
    await this.repository.save(access);
    presenter.displayPromptSharedWithUsers({promptId: access.getPromptId(), userIds: access.toSnapshot().userIds});
  }
}
