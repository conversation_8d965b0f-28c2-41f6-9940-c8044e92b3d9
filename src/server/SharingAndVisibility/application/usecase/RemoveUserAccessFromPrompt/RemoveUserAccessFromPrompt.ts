import {PromptAccessRepository} from '../../port/driven/PromptAccessRepository';
import {RemoveUserAccessFromPromptRequest} from './RemoveUserAccessFromPromptRequest';
import {RemoveUserAccessFromPromptPresenter} from '../../port/driven/RemoveUserAccessFromPromptPresenter';
import {RemoveUserAccessFromPromptUseCase} from '../../port/driving/RemoveUserAccessFromPromptUseCase';

export class RemoveUserAccessFromPrompt implements RemoveUserAccessFromPromptUseCase {
  private readonly repository: PromptAccessRepository;

  constructor(repository: PromptAccessRepository) {
    this.repository = repository;
  }

  async execute(request: RemoveUserAccessFromPromptRequest, presenter: RemoveUserAccessFromPromptPresenter): Promise<void> {
    const {promptId, targetUserId} = request;
    const access = await this.repository.findById(promptId);
    if (!access) return;
    access.removeUser(targetUserId);
    await this.repository.save(access);
    presenter.displayUserAccessRemoved({promptId: access.getPromptId(), userIds: access.toSnapshot().userIds});
  }
}
