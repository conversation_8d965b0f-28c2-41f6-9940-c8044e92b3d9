import { ViewPromptAccessListRequest } from "@src/server/SharingAndVisibility/application/usecase/ViewPromptAccessList/ViewPromptAccessListRequest";
import { ViewPromptAccessListPresenter } from "@src/server/SharingAndVisibility/application/port/driven/ViewPromptAccessListPresenter";

export interface ViewPromptAccessListUseCase {
  execute(request: ViewPromptAccessListRequest, presenter: ViewPromptAccessListPresenter): Promise<void>;
}
