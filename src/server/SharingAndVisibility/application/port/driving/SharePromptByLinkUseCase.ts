import { SharePromptByLinkRequest } from "@src/server/SharingAndVisibility/application/usecase/SharePromptByLink/SharePromptByLinkRequest";
import { SharePromptByLinkPresenter } from "@src/server/SharingAndVisibility/application/port/driven/SharePromptByLinkPresenter";

export interface SharePromptByLinkUseCase {
  execute(request: SharePromptByLinkRequest, presenter: Share<PERSON><PERSON>ptByLinkPresenter): Promise<void>;
}
