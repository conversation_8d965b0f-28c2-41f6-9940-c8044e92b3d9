import { RemoveUserAccessFromPromptRequest } from "@src/server/SharingAndVisibility/application/usecase/RemoveUserAccessFromPrompt/RemoveUserAccessFromPromptRequest";
import { RemoveUserAccessFromPromptPresenter } from "@src/server/SharingAndVisibility/application/port/driven/RemoveUserAccessFromPromptPresenter";

export interface RemoveUserAccessFromPromptUseCase {
  execute(request: RemoveUserAccessFromPromptRequest, presenter: RemoveUserAccessFromPromptPresenter): Promise<void>;
}
