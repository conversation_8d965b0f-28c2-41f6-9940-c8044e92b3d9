import { SetPromptVisibilityRequest } from "@src/server/SharingAndVisibility/application/usecase/SetPromptVisibility/SetPromptVisibilityRequest";
import { SetPromptVisibilityPresenter } from "@src/server/SharingAndVisibility/application/port/driven/SetPromptVisibilityPresenter";

export interface SetPromptVisibilityUseCase {
  execute(request: SetPromptVisibilityRequest, presenter: SetPromptVisibilityPresenter): Promise<void>;
}
