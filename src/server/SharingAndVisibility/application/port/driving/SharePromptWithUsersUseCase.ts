import { SharePromptWithUsersRequest } from "@src/server/SharingAndVisibility/application/usecase/SharePromptWithUsers/SharePromptWithUsersRequest";
import { SharePromptWithUsersPresenter } from "@src/server/SharingAndVisibility/application/port/driven/SharePromptWithUsersPresenter";

export interface SharePromptWithUsersUseCase {
  execute(request: SharePromptWithUsersRequest, presenter: SharePromptWithUsersPresenter): Promise<void>;
}
