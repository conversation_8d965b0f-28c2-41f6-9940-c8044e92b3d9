import {createContainer, type Container} from '@evyweb/ioctopus';
import {sharedModule} from './Shared';
import {authenticationModule} from './Authentication';
import {collaborationModule} from './Collaboration';
import {promptManagementModule} from './PromptManagement';
import {sharingAndVisibilityModule} from './SharingAndVisibility';
import {discoveryModule} from './Discovery';

export function createServerContainer(): Container {
  const container = createContainer();

  container.load('Shared', sharedModule);
  container.load('Authentication', authenticationModule);
  container.load('PromptManagement', promptManagementModule);
  container.load('Collaboration', collaborationModule);
  container.load('SharingAndVisibility', sharingAndVisibilityModule);
  container.load('Discovery', discoveryModule);

  return container;
}
