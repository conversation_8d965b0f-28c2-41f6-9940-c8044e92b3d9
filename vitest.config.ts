import { defineConfig, configDefaults } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    globals: true,
    exclude: [...configDefaults.exclude, 'apps/{client,server}/**/*.spec.{ts,tsx}'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html'],
      all: true,
      include: ['src/**/*.{ts,tsx}'],
      exclude: [
        'src/**/*Request.ts',
        'src/**/*Response.ts',
        'src/**/*ViewModel.ts',
        'src/**/*Snapshot.ts',
        'src/**/port/**',
        'src/**/index.ts'
      ]
    }
  },
  resolve: {
    alias: {
      '@src': resolve(__dirname, 'src')
    }
  }
});
