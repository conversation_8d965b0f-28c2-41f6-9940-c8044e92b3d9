{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@src/*": ["src/*"]}, "types": ["vitest/globals", "react", "react-dom"], "jsx": "react-jsx", "lib": ["ES2020", "DOM", "DOM.Iterable"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "apps/server/**/*.ts"], "exclude": ["node_modules"]}