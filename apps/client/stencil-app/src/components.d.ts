/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
export namespace Components {
    interface MyGreeter {
        /**
          * @default "Test"
         */
        "name": string;
    }
}
declare global {
    interface HTMLMyGreeterElement extends Components.MyGreeter, HTMLStencilElement {
    }
    var HTMLMyGreeterElement: {
        prototype: HTMLMyGreeterElement;
        new (): HTMLMyGreeterElement;
    };
    interface HTMLElementTagNameMap {
        "my-greeter": HTMLMyGreeterElement;
    }
}
declare namespace LocalJSX {
    interface MyGreeter {
        /**
          * @default "Test"
         */
        "name"?: string;
    }
    interface IntrinsicElements {
        "my-greeter": MyGreeter;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "my-greeter": LocalJSX.MyGreeter & JSXBase.HTMLAttributes<HTMLMyGreeterElement>;
        }
    }
}
