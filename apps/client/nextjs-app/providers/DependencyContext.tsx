'use client';

import { createContext, useContext, useMemo } from 'react';
import type { Container } from '@evyweb/ioctopus';
import { createClientContainer } from '@src/client/DependencyInjection';

const DependencyContext = createContext<Container | null>(null);

export function DependencyProvider({ baseUrl, children }: { baseUrl: string; children: React.ReactNode }) {
  const container = useMemo(() => createClientContainer({ baseUrl }), [baseUrl]);
  return <DependencyContext.Provider value={container}>{children}</DependencyContext.Provider>;
}

export function useDependency<T>(key: string): T {
  const container = useContext(DependencyContext);
  if (!container) {
    throw new Error('Dependency container not provided');
  }
  return container.get<T>(key);
}

export { DependencyContext };
