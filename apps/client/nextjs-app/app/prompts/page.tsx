"use client";
import { useState } from "react";
import { <PERSON><PERSON> } from "@design-system/nextjs";
import type { Prompt, PromptFilters } from "@design-system/nextjs";
import {PromptEditor} from "@src/client/PromptManagement/infrastructure/components/PromptEditor";
import {usePromptData} from "@src/client/Discovery/infrastructure/hooks/use-prompt-data";
import {PromptDetailView} from "@src/client/PromptManagement/infrastructure/components/PromptDetailView";
import {mockComments} from "@src/client/Collaboration/infrastructure/mocks/data";
import {SearchBar} from "@src/client/Discovery/infrastructure/components/SearchBar";
import {FilterMenu} from "@src/client/Discovery/infrastructure/components/FilterMenu";
import {FilterChips} from "@src/client/Discovery/infrastructure/components/FilterChips";
import {PromptList} from "@src/client/PromptManagement/infrastructure/components/PromptList";
export default function PromptsPage() {
  const { filteredPrompts, filters, updateFilters, clearFilters } = usePromptData();
  const [search, setSearch] = useState(filters.searchText || "");
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const onSearchChange = (value: string) => {
    setSearch(value);
    updateFilters({ searchText: value || undefined });
  };

  const onRemoveFilter = (type: keyof PromptFilters, value?: string) => {
    if (type === "tags" && value) {
      const nextTags = (filters.tags || []).filter((id) => id !== value);
      updateFilters({ tags: nextTags.length ? nextTags : undefined });
    } else {
      updateFilters({ [type]: undefined } as Partial<PromptFilters>);
    }
  };

  const handleBack = () => {
    setSelectedPrompt(null);
    setIsEditing(false);
    setIsCreating(false);
  };

  const handleEdit = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
    setIsEditing(true);
  };

  const handleCreate = () => {
    setIsCreating(true);
    setSelectedPrompt(null);
    setIsEditing(false);
  };

  if (isCreating) {
    return (
      <div className="space-y-6">
        <PromptEditor onBack={handleBack} onCancel={handleBack} />
      </div>
    );
  }

  if (isEditing && selectedPrompt) {
    return (
      <div className="space-y-6">
        <PromptEditor prompt={selectedPrompt} onBack={handleBack} onCancel={handleBack} />
      </div>
    );
  }

  if (selectedPrompt) {
    return (
      <PromptDetailView
        prompt={selectedPrompt}
        comments={mockComments}
        onBack={handleBack}
        onEdit={handleEdit}
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Prompts</h1>
        <Button onClick={handleCreate}>Create Prompt</Button>
      </div>

      <div className="flex items-center gap-2">
        <div className="flex-1">
          <SearchBar value={search} onChange={onSearchChange} onClear={() => onSearchChange("")} />
        </div>
        <FilterMenu filters={filters} onFilterChange={updateFilters} />
      </div>

      <FilterChips filters={filters} onRemoveFilter={onRemoveFilter} onClearAll={clearFilters} />

      <PromptList prompts={filteredPrompts} onViewPrompt={setSelectedPrompt} onEditPrompt={handleEdit} />
    </div>
  );
}
