"use client";
import Link from "next/link";
import { Theme<PERSON><PERSON><PERSON>, User<PERSON><PERSON>ar, But<PERSON> } from "@design-system/nextjs";
import {NotificationsDropdown} from "@src/client/Collaboration/infrastructure/components/NotificationsDropdown";

export default function Home() {
  return (
    <div className="min-h-dvh flex flex-col">
      <header className="border-b">
        <div className="mx-auto max-w-7xl px-4 py-3 flex items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <Link href="/" className="text-lg font-semibold hover:text-primary">
              Prompt Hub
            </Link>
          </div>
          <div className="flex items-center gap-2">
            <NotificationsDropdown />
            <ThemeToggle />
            <UserAvatar />
          </div>
        </div>
      </header>

      <main className="flex-1 mx-auto w-full max-w-7xl px-4 py-6">
        <div className="text-center space-y-6">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold">Welcome to Prompt Hub</h1>
            <p className="text-lg text-muted-foreground">
              Discover, create, and share AI prompts with the community
            </p>
          </div>

          <div className="flex items-center justify-center gap-4">
            <Button asChild size="lg">
              <Link href="/prompts">Browse Prompts</Link>
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
}
