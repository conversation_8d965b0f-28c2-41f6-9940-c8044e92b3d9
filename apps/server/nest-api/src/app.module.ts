import { Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PromptManagementController } from './boundedContexts/PromptManagement.controller';
import { CollaborationController } from './boundedContexts/Collaboration.controller';
import { SharingAndVisibilityController } from './boundedContexts/SharingAndVisibility.controller';
import { DiscoveryController } from './boundedContexts/Discovery.controller';
import { createServerContainer } from '@src/server/DependencyInjection';
import type { Container } from '@evyweb/ioctopus';

@Module({
  imports: [],
  controllers: [
    AppController,
    PromptManagementController,
    CollaborationController,
    SharingAndVisibilityController,
    DiscoveryController,
  ],
  providers: [
    AppService,
    {
      provide: 'ServerContainer',
      useFactory: (): Container => createServerContainer(),
    },
  ],
})
export class AppModule {}
