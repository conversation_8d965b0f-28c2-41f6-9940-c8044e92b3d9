import { readFileSync } from 'fs';
import { join } from 'path';

describe('When requesting the OpenAPI document', () => {
  it('should expose all api endpoints', async () => {
    // Act
    const doc = JSON.parse(
      readFileSync(join(__dirname, 'openapi.json'), 'utf-8'),
    );

    // Assert
    expect(doc.paths['/prompts']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/draft']?.put).toBeDefined();
    expect(doc.paths['/prompts/:id/publish']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/duplicate']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id']?.delete).toBeDefined();
    expect(doc.paths['/prompts/:id/archive']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/restore']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/target-ai']?.put).toBeDefined();
    expect(doc.paths['/prompts/:id/favorite']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/favorite']?.delete).toBeDefined();
    expect(doc.paths['/prompts/:id/tags']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/tags/:tag']?.delete).toBeDefined();
    expect(doc.paths['/prompts/:id/comments']?.post).toBeDefined();
    expect(doc.paths['/comments/:id']?.put).toBeDefined();
    expect(doc.paths['/comments/:id']?.delete).toBeDefined();
    expect(doc.paths['/notifications/:id/read']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/visibility']?.put).toBeDefined();
    expect(doc.paths['/prompts/:id/share/link']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/share/users']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/share/users/:userId']?.delete).toBeDefined();
    expect(doc.paths['/prompts/:id/access']?.get).toBeDefined();
    expect(doc.paths['/prompts/search/:text']?.get).toBeDefined();
    expect(doc.paths['/prompts/tags/:tag']?.get).toBeDefined();
    expect(doc.paths['/prompts/target-ai/:targetAI']?.get).toBeDefined();
    expect(doc.paths['/prompts/authors/:authorId']?.get).toBeDefined();
    expect(doc.paths['/prompts/authors/:authorId/shared-with/:userId']?.get).toBeDefined();
    expect(doc.paths['/prompts/status/:status']?.get).toBeDefined();
    expect(doc.paths['/prompts/favorites/:userId']?.get).toBeDefined();
    expect(doc.paths['/prompts/:id/notifications/shared-with-me']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/notifications/new-comment']?.post).toBeDefined();
    expect(doc.paths['/prompts/:id/comments/stream']?.get).toBeDefined();

    expect(doc.paths['/prompts']?.post.tags).toEqual(['PromptManagement']);
    expect(doc.paths['/prompts/:id/comments']?.post.tags).toEqual(['Collaboration']);
    expect(doc.paths['/prompts/:id/visibility']?.put.tags).toEqual(['SharingAndVisibility']);
    expect(doc.paths['/prompts/search/:text']?.get.tags).toEqual(['Discovery']);
  });
});
