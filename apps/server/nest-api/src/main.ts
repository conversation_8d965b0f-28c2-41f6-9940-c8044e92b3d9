import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { readFileSync } from 'fs';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const document = JSON.parse(
    readFileSync(join(__dirname, 'openapi.json'), 'utf-8'),
  );
  app.use('/doc', (_req, res) => res.json(document));
  app.use('/ui', (_req, res) => {
    res.send(
      `<html><body><redoc spec-url='/doc'></redoc><script src='https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js'></script></body></html>`,
    );
  });

  console.log('Nest API listening on port 3002');
  await app.listen(3002);
}
bootstrap();
