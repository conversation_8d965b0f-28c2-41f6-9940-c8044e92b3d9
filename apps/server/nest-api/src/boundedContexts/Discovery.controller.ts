import {Controller, Get, Inject, Param, Res} from '@nestjs/common';
import {Response} from 'express';
import type {Container} from '@evyweb/ioctopus';
import {
  SearchPromptsByTextApiController
} from '@src/server/Discovery/presentation/api/SearchPromptsByText/SearchPromptsByTextApiController';
import {
  FilterPromptsByTagApiController
} from '@src/server/Discovery/presentation/api/FilterPromptsByTag/FilterPromptsByTagApiController';
import {
  FilterPromptsByTargetAIApiController
} from '@src/server/Discovery/presentation/api/FilterPromptsByTargetAI/FilterPromptsByTargetAIApiController';
import {
  FilterPromptsByAuthorApiController
} from '@src/server/Discovery/presentation/api/FilterPromptsByAuthor/FilterPromptsByAuthorApiController';
import {
  FilterPromptsByAuthorOrSharedApiController
} from '@src/server/Discovery/presentation/api/FilterPromptsByAuthorOrShared/FilterPromptsByAuthorOrSharedApiController';
import {
  ListPromptsByStatusApiController
} from '@src/server/Discovery/presentation/api/ListPromptsByStatus/ListPromptsByStatusApiController';
import {
  ListPromptsByStatusRequest
} from '@src/server/Discovery/application/usecase/ListPromptsByStatus/ListPromptsByStatusRequest';
import {
  ListFavoritePromptsApiController
} from '@src/server/Discovery/presentation/api/ListFavoritePrompts/ListFavoritePromptsApiController';

@Controller()
export class DiscoveryController {
  constructor(@Inject('ServerContainer') private readonly container: Container) {
  }

  @Get('/prompts/search/:text')
  async searchByText(@Param('text') text: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<SearchPromptsByTextApiController>('SearchPromptsByTextController');
    const viewModel = await controller.handle({text});
    res.status(viewModel.status).json(viewModel.body);
  }

  @Get('/prompts/tags/:tag')
  async filterByTag(@Param('tag') tag: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<FilterPromptsByTagApiController>('FilterPromptsByTagController');
    const viewModel = await controller.handle({tag});
    res.status(viewModel.status).json(viewModel.body);
  }

  @Get('/prompts/target-ai/:targetAI')
  async filterByTargetAI(@Param('targetAI') targetAI: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<FilterPromptsByTargetAIApiController>('FilterPromptsByTargetAIController');
    const viewModel = await controller.handle({targetAI});
    res.status(viewModel.status).json(viewModel.body);
  }

  @Get('/prompts/authors/:authorId')
  async filterByAuthor(@Param('authorId') authorId: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<FilterPromptsByAuthorApiController>('FilterPromptsByAuthorController');
    const viewModel = await controller.handle({authorId});
    res.status(viewModel.status).json(viewModel.body);
  }

  @Get('/prompts/authors/:authorId/shared-with/:userId')
  async filterByAuthorOrShared(@Param('authorId') authorId: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<FilterPromptsByAuthorOrSharedApiController>('FilterPromptsByAuthorOrSharedController');
    const viewModel = await controller.handle({authorId});
    res.status(viewModel.status).json(viewModel.body);
  }

  @Get('/prompts/status/:status')
  async listByStatus(@Param('status') status: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<ListPromptsByStatusApiController>('ListPromptsByStatusController');
    const viewModel = await controller.handle({status: status as ListPromptsByStatusRequest['status']});
    res.status(viewModel.status).json(viewModel.body);
  }

  @Get('/prompts/favorites/:userId')
  async listFavorites(@Param('userId') userId: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<ListFavoritePromptsApiController>('ListFavoritePromptsController');
    const viewModel = await controller.handle();
    res.status(viewModel.status).json(viewModel.body);
  }
}
