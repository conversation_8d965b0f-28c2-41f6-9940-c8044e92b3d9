import { Body, Controller, Delete, Get, Inject, Param, Post, Put, Res } from '@nestjs/common';
import { Response } from 'express';
import type { Container } from '@evyweb/ioctopus';
import { ExpressSseStreamingApi } from '@src/server/Shared/infrastructure/Sse/ExpressSseStreamingApi';
import { AddCommentToPromptApiController } from '@src/server/Collaboration/presentation/api/AddCommentToPrompt/AddCommentToPromptApiController';
import { EditCommentApiController } from '@src/server/Collaboration/presentation/api/EditComment/EditCommentApiController';
import { DeleteCommentApiController } from '@src/server/Collaboration/presentation/api/DeleteComment/DeleteCommentApiController';
import { MarkNotificationAsReadApiController } from '@src/server/Collaboration/presentation/api/MarkNotificationAsRead/MarkNotificationAsReadApiController';
import { NotifyPromptSharedWithMeApiController } from '@src/server/Collaboration/presentation/api/NotifyPromptSharedWithMe/NotifyPromptSharedWithMeApiController';
import { NotifyNewCommentOnFollowedPromptApiController } from '@src/server/Collaboration/presentation/api/NotifyNewCommentOnFollowedPrompt/NotifyNewCommentOnFollowedPromptApiController';
import { ReceivePromptCommentsInRealtimeApiController } from '@src/server/Collaboration/presentation/api/ReceivePromptCommentsInRealtime/ReceivePromptCommentsInRealtimeApiController';

@Controller()
export class CollaborationController {
  constructor(@Inject('ServerContainer') private readonly container: Container) {}
  @Post('/prompts/:id/comments')
  async addComment(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<AddCommentToPromptApiController>('AddCommentToPromptController');
    const viewModel = await controller.handle({ promptId: id, content: body.content });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Put('/comments/:id')
  async editComment(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<EditCommentApiController>('EditCommentController');
    const viewModel = await controller.handle({ commentId: id, content: body.content });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Delete('/comments/:id')
  async deleteComment(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<DeleteCommentApiController>('DeleteCommentController');
    const viewModel = await controller.handle({ commentId: id });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/notifications/:id/read')
  async markNotificationRead(@Param('id') id: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<MarkNotificationAsReadApiController>('MarkNotificationAsReadController');
    const viewModel = await controller.handle({ notificationId: id });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/notifications/shared-with-me')
  async notifyPromptShared(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<NotifyPromptSharedWithMeApiController>('NotifyPromptSharedWithMeController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/notifications/new-comment')
  async notifyNewComment(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<NotifyNewCommentOnFollowedPromptApiController>('NotifyNewCommentOnFollowedPromptController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Get('/prompts/:id/comments/stream')
  async receiveComments(@Param('id') id: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<ReceivePromptCommentsInRealtimeApiController>('ReceivePromptCommentsInRealtimeController');
    const stream = new ExpressSseStreamingApi(res);
    controller.handle(stream as any, { promptId: id });
  }
}
