import { Body, Controller, Delete, Get, Inject, Param, Post, Put, Res } from '@nestjs/common';
import { Response } from 'express';
import type { Container } from '@evyweb/ioctopus';
import { SetPromptVisibilityApiController } from '@src/server/SharingAndVisibility/presentation/api/SetPromptVisibility/SetPromptVisibilityApiController';
import { SharePromptByLinkApiController } from '@src/server/SharingAndVisibility/presentation/api/SharePromptByLink/SharePromptByLinkApiController';
import { SharePromptWithUsersApiController } from '@src/server/SharingAndVisibility/presentation/api/SharePromptWithUsers/SharePromptWithUsersApiController';
import { RemoveUserAccessFromPromptApiController } from '@src/server/SharingAndVisibility/presentation/api/RemoveUserAccessFromPrompt/RemoveUserAccessFromPromptApiController';
import { ViewPromptAccessListApiController } from '@src/server/SharingAndVisibility/presentation/api/ViewPromptAccessList/ViewPromptAccessListApiController';

@Controller()
export class SharingAndVisibilityController {
  constructor(@Inject('ServerContainer') private readonly container: Container) {}
  @Put('/prompts/:id/visibility')
  async setVisibility(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<SetPromptVisibilityApiController>('SetPromptVisibilityController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/share/link')
  async shareByLink(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<SharePromptByLinkApiController>('SharePromptByLinkController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/share/users')
  async shareWithUsers(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<SharePromptWithUsersApiController>('SharePromptWithUsersController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Delete('/prompts/:id/share/users/:userId')
  async removeUserAccess(@Param('id') id: string, @Param('userId') userId: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<RemoveUserAccessFromPromptApiController>('RemoveUserAccessFromPromptController');
    const viewModel = await controller.handle({ promptId: id, targetUserId: userId });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Get('/prompts/:id/access')
  async viewAccess(@Param('id') id: string, @Res() res: Response): Promise<void> {
    const controller = this.container.get<ViewPromptAccessListApiController>('ViewPromptAccessListController');
    const viewModel = await controller.handle({ promptId: id });
    res.status(viewModel.status).json(viewModel.body);
  }
}
