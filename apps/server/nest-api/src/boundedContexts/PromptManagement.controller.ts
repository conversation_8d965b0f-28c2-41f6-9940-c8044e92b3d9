import { Body, Controller, Delete, Inject, Param, Post, Put, Res } from '@nestjs/common';
import { Response } from 'express';
import type { Container } from '@evyweb/ioctopus';
import { AddPromptToFavoritesApiController } from '@src/server/PromptManagement/presentation/api/AddPromptToFavorites/AddPromptToFavoritesApiController';
import { RemovePromptFromFavoritesApiController } from '@src/server/PromptManagement/presentation/api/RemovePromptFromFavorites/RemovePromptFromFavoritesApiController';
import { AddTagToPromptApiController } from '@src/server/PromptManagement/presentation/api/AddTagToPrompt/AddTagToPromptApiController';
import { RemoveTagFromPromptApiController } from '@src/server/PromptManagement/presentation/api/RemoveTagFromPrompt/RemoveTagFromPromptApiController';
import { CreatePromptDraftApiController } from '@src/server/PromptManagement/presentation/api/CreatePromptDraft/CreatePromptDraftApiController';
import { EditPromptDraftApiController } from '@src/server/PromptManagement/presentation/api/EditPromptDraft/EditPromptDraftApiController';
import { PublishPromptApiController } from '@src/server/PromptManagement/presentation/api/PublishPrompt/PublishPromptApiController';
import { DuplicatePromptApiController } from '@src/server/PromptManagement/presentation/api/DuplicatePrompt/DuplicatePromptApiController';
import { DeletePromptApiController } from '@src/server/PromptManagement/presentation/api/DeletePrompt/DeletePromptApiController';
import { ArchivePromptApiController } from '@src/server/PromptManagement/presentation/api/ArchivePrompt/ArchivePromptApiController';
import { RestorePromptApiController } from '@src/server/PromptManagement/presentation/api/RestorePrompt/RestorePromptApiController';
import { ChangePromptTargetAIApiController } from '@src/server/PromptManagement/presentation/api/ChangePromptTarget/ChangePromptTargetAIApiController';

@Controller()
export class PromptManagementController {
  constructor(@Inject('ServerContainer') private readonly container: Container) {}
  @Post('/prompts')
  async createPromptDraft(@Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<CreatePromptDraftApiController>('CreatePromptDraftController');
    const viewModel = await controller.handle(body);
    res.status(viewModel.status).json(viewModel.body);
  }

  @Put('/prompts/:id/draft')
  async editPromptDraft(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<EditPromptDraftApiController>('EditPromptDraftController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/publish')
  async publishPrompt(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<PublishPromptApiController>('PublishPromptController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/duplicate')
  async duplicatePrompt(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<DuplicatePromptApiController>('DuplicatePromptController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Delete('/prompts/:id')
  async deletePrompt(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<DeletePromptApiController>('DeletePromptController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/archive')
  async archivePrompt(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<ArchivePromptApiController>('ArchivePromptController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/restore')
  async restorePrompt(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<RestorePromptApiController>('RestorePromptController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Put('/prompts/:id/target-ai')
  async changeTargetAI(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<ChangePromptTargetAIApiController>('ChangePromptTargetAIController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/favorite')
  async addFavorite(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<AddPromptToFavoritesApiController>('AddPromptToFavoritesController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Delete('/prompts/:id/favorite')
  async removeFavorite(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<RemovePromptFromFavoritesApiController>('RemovePromptFromFavoritesController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Post('/prompts/:id/tags')
  async addTag(@Param('id') id: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<AddTagToPromptApiController>('AddTagToPromptController');
    const viewModel = await controller.handle({ promptId: id, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }

  @Delete('/prompts/:id/tags/:tag')
  async removeTag(@Param('id') id: string, @Param('tag') tag: string, @Body() body: any, @Res() res: Response): Promise<void> {
    const controller = this.container.get<RemoveTagFromPromptApiController>('RemoveTagFromPromptController');
    const viewModel = await controller.handle({ promptId: id, tag, ...body });
    res.status(viewModel.status).json(viewModel.body);
  }
}
