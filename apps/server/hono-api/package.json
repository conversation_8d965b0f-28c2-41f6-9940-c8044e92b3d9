{"name": "hono-api", "type": "module", "scripts": {"test": "vitest run", "dev": "cross-env TS_NODE_PROJECT=../../../tsconfig.json tsx watch -r tsconfig-paths/register src/index.ts --port 3001", "build": "tsc", "start": "npm run build && node dist/index.js"}, "dependencies": {"@hono/node-server": "^1.14.4", "hono": "^4.8.3", "@hono/swagger-ui": "^0.5.2", "@hono/zod-openapi": "^0.19.9", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20.11.17", "tsx": "^4.7.1", "typescript": "^5.8.3", "cross-env": "^7.0.3"}}