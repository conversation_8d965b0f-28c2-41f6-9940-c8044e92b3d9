import {OpenAPIHono} from '@hono/zod-openapi';
import {swaggerUI} from '@hono/swagger-ui';
import {createServerContainer} from '@src/server/DependencyInjection';
import {PromptManagementApp} from './boundedContexts/PromptManagementApp';
import {CollaborationApp} from './boundedContexts/CollaborationApp';
import {SharingAndVisibilityApp} from './boundedContexts/SharingAndVisibilityApp';
import {DiscoveryApp} from './boundedContexts/DiscoveryApp';
import {AuthenticationApp} from './boundedContexts/AuthenticationApp';
import type {AuthenticationGateway} from "@src/server/Authentication";

export class App extends OpenAPIHono {
  readonly container = createServerContainer();

  constructor() {
    super();

    this.openAPIRegistry.registerComponent('securitySchemes', 'bearerAuth', {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
    });

    const gateway = this.container.get<AuthenticationGateway>('AuthenticationGateway');
    this.use('*', async (c, next) => {
      let token = c.req.header('Authorization') ?? null;
      if (token?.startsWith('Bearer ')) {
        token = token.slice(7);
      }

      gateway.setCurrentUserIdFromToken(token);
      await next();
    });

    this.route('/', new AuthenticationApp(this.container));
    this.route('/', new PromptManagementApp(this.container));
    this.route('/', new CollaborationApp(this.container));
    this.route('/', new SharingAndVisibilityApp(this.container));
    this.route('/', new DiscoveryApp(this.container));

    this.get('/ui', swaggerUI({url: '/doc', persistAuthorization: true}));
    this.doc('/doc', {
      openapi: '3.0.0',
      info: {
        title: 'Clean Architecture API',
        version: '1.0.0',
      },
      security: [{bearerAuth: []}],
    });
  }
}
