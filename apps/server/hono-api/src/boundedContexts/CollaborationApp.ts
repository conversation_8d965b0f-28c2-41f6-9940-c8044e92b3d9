import {create<PERSON>oute, OpenAPIHono, z} from '@hono/zod-openapi';
import {streamSSE} from 'hono/streaming';
import type {Container} from '@evyweb/ioctopus';
import {
  AddCommentToPromptApiController
} from '@src/server/Collaboration/presentation/api/AddCommentToPrompt/AddCommentToPromptApiController';
import {
  EditCommentApiController
} from '@src/server/Collaboration/presentation/api/EditComment/EditCommentApiController';
import {
  DeleteCommentApiController
} from '@src/server/Collaboration/presentation/api/DeleteComment/DeleteCommentApiController';
import {
  MarkNotificationAsReadApiController
} from '@src/server/Collaboration/presentation/api/MarkNotificationAsRead/MarkNotificationAsReadApiController';
import {
  NotifyPromptSharedWithMeApiController
} from '@src/server/Collaboration/presentation/api/NotifyPromptSharedWithMe/NotifyPromptSharedWithMeApiController';
import {
  NotifyNewCommentOnFollowedPromptApiController
} from '@src/server/Collaboration/presentation/api/NotifyNewCommentOnFollowedPrompt/NotifyNewCommentOnFollowedPromptApiController';
import {
  ReceivePromptCommentsInRealtimeApiController
} from '@src/server/Collaboration/presentation/api/ReceivePromptCommentsInRealtime/ReceivePromptCommentsInRealtimeApiController';

const TAG = 'Collaboration';

export class CollaborationApp extends OpenAPIHono {
  private readonly container: Container;

  constructor(container: Container) {
    super();
    this.container = container;
    const addCommentController = this.container.get<AddCommentToPromptApiController>('AddCommentToPromptController');
    const editCommentController = this.container.get<EditCommentApiController>('EditCommentController');
    const deleteCommentController = this.container.get<DeleteCommentApiController>('DeleteCommentController');
    const markNotificationAsReadController = this.container.get<MarkNotificationAsReadApiController>('MarkNotificationAsReadController');
    const notifyPromptSharedWithMeController = this.container.get<NotifyPromptSharedWithMeApiController>('NotifyPromptSharedWithMeController');
    const notifyNewCommentOnFollowedPromptController = this.container.get<NotifyNewCommentOnFollowedPromptApiController>('NotifyNewCommentOnFollowedPromptController');
    const receiveCommentsRealtimeController = this.container.get<ReceivePromptCommentsInRealtimeApiController>('ReceivePromptCommentsInRealtimeController');

    const addCommentRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/comments',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {
          content: {'application/json': {schema: z.object({content: z.string()})}},
        },
      },
      responses: {
        201: {description: 'Comment added', content: {'application/json': {schema: z.object({commentId: z.string()})}}},
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(addCommentRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await addCommentController.handle({promptId, ...body});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 201) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const editCommentRoute = createRoute({
      method: 'put',
      path: '/comments/{id}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'comment-123' }),
        }),
        body: {content: {'application/json': {schema: z.object({content: z.string()})}}},
      },
      responses: {
        200: {
          description: 'Comment edited',
          content: {'application/json': {schema: z.object({commentId: z.string()})}}
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(editCommentRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: commentId } = c.req.valid('param');
      const viewModel = await editCommentController.handle({commentId, ...body});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const deleteCommentRoute = createRoute({
      method: 'delete',
      path: '/comments/{id}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'comment-123' }),
        }),
      },
      responses: {
        200: {
          description: 'Comment deleted',
          content: {'application/json': {schema: z.object({commentId: z.string()})}}
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(deleteCommentRoute, async (c) => {
      const { id: commentId } = c.req.valid('param');
      const viewModel = await deleteCommentController.handle({commentId});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const markNotificationReadRoute = createRoute({
      method: 'post',
      path: '/notifications/{id}/read',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'notification-123' }),
        }),
      },
      responses: {
        200: {
          description: 'Notification marked as read',
          content: {'application/json': {schema: z.object({notificationId: z.string()})}}
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(markNotificationReadRoute, async (c) => {
      const { id: notificationId } = c.req.valid('param');
      const viewModel = await markNotificationAsReadController.handle({notificationId});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const notifyPromptSharedRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/notifications/shared-with-me',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {content: {'application/json': {schema: z.object({userId: z.string()})}}}
      },
      responses: {
        201: {
          description: 'User notified about shared prompt',
          content: {'application/json': {schema: z.object({notificationId: z.string()})}}
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(notifyPromptSharedRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await notifyPromptSharedWithMeController.handle({promptId, ...body});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 201) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const notifyNewCommentRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/notifications/new-comment',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {content: {'application/json': {schema: z.object({userId: z.string()})}}}
      },
      responses: {
        201: {
          description: 'Follower notified about new comment',
          content: {'application/json': {schema: z.object({notificationId: z.string()})}}
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(notifyNewCommentRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await notifyNewCommentOnFollowedPromptController.handle({promptId, ...body});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 201) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const receiveCommentsRoute = createRoute({
      method: 'get',
      path: '/prompts/{id}/comments/stream',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
      },
      responses: {200: {description: 'Comments stream'}},
    });

    this.openapi(receiveCommentsRoute, (c) => {
      const { id: promptId } = c.req.valid('param');
      return streamSSE(c, async (stream) => {
        receiveCommentsRealtimeController.handle(stream, {promptId});
      });
    });
  }
}
