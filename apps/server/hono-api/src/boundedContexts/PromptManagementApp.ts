import {create<PERSON>out<PERSON>, OpenAPIHono, z} from '@hono/zod-openapi';
import type {Container} from '@evyweb/ioctopus';
import {
  AddPromptToFavoritesApiController
} from '@src/server/PromptManagement/presentation/api/AddPromptToFavorites/AddPromptToFavoritesApiController';
import {
  RemovePromptFromFavoritesApiController
} from '@src/server/PromptManagement/presentation/api/RemovePromptFromFavorites/RemovePromptFromFavoritesApiController';
import {
  AddTagToPromptApiController
} from '@src/server/PromptManagement/presentation/api/AddTagToPrompt/AddTagToPromptApiController';
import {
  RemoveTagFromPromptApiController
} from '@src/server/PromptManagement/presentation/api/RemoveTagFromPrompt/RemoveTagFromPromptApiController';
import {
  CreatePromptDraftApiController
} from '@src/server/PromptManagement/presentation/api/CreatePromptDraft/CreatePromptDraftApiController';
import {
  EditPromptDraftApiController
} from '@src/server/PromptManagement/presentation/api/EditPromptDraft/EditPromptDraftApiController';
import {
  PublishPromptApiController
} from '@src/server/PromptManagement/presentation/api/PublishPrompt/PublishPromptApiController';
import {
  DuplicatePromptApiController
} from '@src/server/PromptManagement/presentation/api/DuplicatePrompt/DuplicatePromptApiController';
import {
  DeletePromptApiController
} from '@src/server/PromptManagement/presentation/api/DeletePrompt/DeletePromptApiController';
import {
  ArchivePromptApiController
} from '@src/server/PromptManagement/presentation/api/ArchivePrompt/ArchivePromptApiController';
import {
  RestorePromptApiController
} from '@src/server/PromptManagement/presentation/api/RestorePrompt/RestorePromptApiController';
import {
  ChangePromptTargetAIApiController
} from '@src/server/PromptManagement/presentation/api/ChangePromptTarget/ChangePromptTargetAIApiController';

const TAG = 'PromptManagement';

export class PromptManagementApp extends OpenAPIHono {
  private readonly container: Container;

  constructor(container: Container) {
    super();
    this.container = container;
    const addFavoriteController = this.container.get<AddPromptToFavoritesApiController>('AddPromptToFavoritesController');
    const removeFavoriteController = this.container.get<RemovePromptFromFavoritesApiController>('RemovePromptFromFavoritesController');
    const addTagController = this.container.get<AddTagToPromptApiController>('AddTagToPromptController');
    const removeTagController = this.container.get<RemoveTagFromPromptApiController>('RemoveTagFromPromptController');
    const createDraftController = this.container.get<CreatePromptDraftApiController>('CreatePromptDraftController');
    const editDraftController = this.container.get<EditPromptDraftApiController>('EditPromptDraftController');
    const publishController = this.container.get<PublishPromptApiController>('PublishPromptController');
    const duplicateController = this.container.get<DuplicatePromptApiController>('DuplicatePromptController');
    const deleteController = this.container.get<DeletePromptApiController>('DeletePromptController');
    const archiveController = this.container.get<ArchivePromptApiController>('ArchivePromptController');
    const restoreController = this.container.get<RestorePromptApiController>('RestorePromptController');
    const changeTargetAIController = this.container.get<ChangePromptTargetAIApiController>('ChangePromptTargetAIController');

    const createPromptRoute = createRoute({
      method: 'post',
      path: '/prompts',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        body: {
          content: {
            'application/json': {
              schema: z.object({
                content: z.string(),
                targetAI: z.string(),
              }),
            },
          },
        },
      },
      responses: {
        201: {
          description: 'Prompt draft created',
          content: {
            'application/json': {
              schema: z.object({ promptId: z.string() }),
            },
          },
        },
        401: {
          description: 'Not authenticated',
          content: {
            'application/json': {
              schema: z.object({ error: z.string() }),
            },
          },
        },
      },
    });

    this.openapi(createPromptRoute, async (c) => {
      const body = c.req.valid('json');
      const viewModel = await createDraftController.handle(body);

      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 201) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const editDraftRoute = createRoute({
      method: 'put',
      path: '/prompts/{id}/draft',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {
          content: {
            'application/json': {
              schema: z.object({content: z.string()}),
            },
          },
        },
      },
      responses: {
        200: {
          description: 'Prompt draft edited',
          content: {
            'application/json': {
              schema: z.object({promptId: z.string()}),
            },
          },
        },
        401: {
          description: 'Not authenticated',
          content: {
            'application/json': { schema: z.object({ error: z.string() }) },
          },
        },
        403: {
          description: 'Not owner of prompt',
          content: {
            'application/json': { schema: z.object({ error: z.string() }) },
          },
        },
        404: {
          description: 'Prompt not found',
          content: {
            'application/json': { schema: z.object({ error: z.string() }) },
          },
        },
      },
    });

    this.openapi(editDraftRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await editDraftController.handle({promptId, ...body});
      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const publishPromptRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/publish',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {
          content: {'application/json': {schema: z.object({})}},
        },
      },
      responses: {
        200: {
          description: 'Prompt published',
          content: { 'application/json': { schema: z.object({ promptId: z.string() }) } },
        },
        401: {
          description: 'Not authenticated',
          content: { 'application/json': { schema: z.object({ error: z.string() }) } },
        },
        403: {
          description: 'Not owner of prompt',
          content: { 'application/json': { schema: z.object({ error: z.string() }) } },
        },
        404: {
          description: 'Prompt not found',
          content: { 'application/json': { schema: z.object({ error: z.string() }) } },
        },
        409: {
          description: 'Prompt already published',
          content: { 'application/json': { schema: z.object({ error: z.string() }) } },
        },
      },
    });

    this.openapi(publishPromptRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');

      const viewModel = await publishController.handle({promptId, ...body});

      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404 ||
        viewModel.status === 409
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const duplicatePromptRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/duplicate',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {
          content: {
            'application/json': {
              schema: z.object({})
            }
          }
        }
      },
      responses: {
        200: {
          description: 'Prompt duplicated',
          content: {
            'application/json': {
              schema: z.object({ promptId: z.string() })
            }
          }
        },
        201: {
          description: 'Prompt duplicated',
          content: {
            'application/json': {
              schema: z.object({ promptId: z.string() })
            }
          }
        },
        401: {
          description: 'Not authenticated',
          content: {
            'application/json': {
              schema: z.object({ error: z.string() })
            }
          }
        },
        403: {
          description: 'Not owner of prompt',
          content: {
            'application/json': {
              schema: z.object({ error: z.string() })
            }
          }
        },
        404: {
          description: 'Prompt not found',
          content: {
            'application/json': {
              schema: z.object({ error: z.string() })
            }
          }
        }
      }
    });

    this.openapi(duplicatePromptRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await duplicateController.handle({
        promptId,
        ...body
      });

      if (viewModel.status === 401 || viewModel.status === 403 || viewModel.status === 404) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 201 || viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const deletePromptRoute = createRoute({
      method: 'delete',
      path: '/prompts/{id}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {content: {'application/json': {schema: z.object({})}}}
      },
      responses: {
        200: {
          description: 'Prompt deleted',
          content: {'application/json': {schema: z.object({promptId: z.string()})}},
        },
        401: {
          description: 'Not authenticated',
          content: {'application/json': {schema: z.object({error: z.string()})}},
        },
        403: {
          description: 'Not owner of prompt',
          content: {'application/json': {schema: z.object({error: z.string()})}},
        },
        404: {
          description: 'Prompt not found',
          content: {'application/json': {schema: z.object({error: z.string()})}},
        },
      },
    });

    this.openapi(deletePromptRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await deleteController.handle({promptId, ...body});
      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const archivePromptRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/archive',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {
          content: {'application/json': {schema: z.object({})}},
        },
      },
      responses: {
        200: {
          description: 'Prompt archived',
          content: {'application/json': {schema: z.object({promptId: z.string()})}},
        },
        401: { description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}} },
        403: { description: 'Not owner of prompt', content: {'application/json': {schema: z.object({error: z.string()})}} },
        404: { description: 'Prompt not found', content: {'application/json': {schema: z.object({error: z.string()})}} },
        409: { description: 'Prompt already archived', content: {'application/json': {schema: z.object({error: z.string()})}} },
      },
    });

    this.openapi(archivePromptRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await archiveController.handle({promptId, ...body});
      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404 ||
        viewModel.status === 409
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const restorePromptRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/restore',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {
          content: {'application/json': {schema: z.object({})}},
        },
      },
      responses: {
        200: {
          description: 'Prompt restored',
          content: {'application/json': {schema: z.object({promptId: z.string()})}},
        },
        401: { description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}} },
        403: { description: 'Not owner of prompt', content: {'application/json': {schema: z.object({error: z.string()})}} },
        404: { description: 'Prompt not found', content: {'application/json': {schema: z.object({error: z.string()})}} },
        409: { description: 'Prompt already restored', content: {'application/json': {schema: z.object({error: z.string()})}} },
      },
    });

    this.openapi(restorePromptRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await restoreController.handle({promptId, ...body});
      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404 ||
        viewModel.status === 409
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const changeTargetAIRoute = createRoute({
      method: 'put',
      path: '/prompts/{id}/target-ai',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {
          content: {
            'application/json': {schema: z.object({targetAI: z.string()})},
          },
        },
      },
      responses: {
        200: {
          description: 'Target AI changed',
          content: {'application/json': {schema: z.object({promptId: z.string()})}},
        },
        401: { description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}} },
        403: { description: 'Not owner of prompt', content: {'application/json': {schema: z.object({error: z.string()})}} },
        404: { description: 'Prompt not found', content: {'application/json': {schema: z.object({error: z.string()})}} },
      },
    });

    this.openapi(changeTargetAIRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await changeTargetAIController.handle({promptId, ...body});
      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const addFavoriteRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/favorite',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {content: {'application/json': {schema: z.object({})}}}
      },
      responses: {
        200: {
          description: 'Prompt favorited',
          content: {'application/json': {schema: z.object({promptId: z.string()})}},
        },
        401: { description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}} },
        403: { description: 'Not owner of prompt', content: {'application/json': {schema: z.object({error: z.string()})}} },
        404: { description: 'Prompt not found', content: {'application/json': {schema: z.object({error: z.string()})}} },
        409: { description: 'Prompt already in favorites', content: {'application/json': {schema: z.object({error: z.string()})}} },
      },
    });

    this.openapi(addFavoriteRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await addFavoriteController.handle({promptId, ...body});
      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404 ||
        viewModel.status === 409
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const removeFavoriteRoute = createRoute({
      method: 'delete',
      path: '/prompts/{id}/favorite',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {content: {'application/json': {schema: z.object({})}}}
      },
      responses: {
        200: {
          description: 'Prompt unfavorited',
          content: {'application/json': {schema: z.object({promptId: z.string()})}},
        },
        401: { description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}} },
        403: { description: 'Not owner of prompt', content: {'application/json': {schema: z.object({error: z.string()})}} },
        404: { description: 'Prompt not found', content: {'application/json': {schema: z.object({error: z.string()})}} },
        409: { description: 'Prompt not in favorites', content: {'application/json': {schema: z.object({error: z.string()})}} },
      },
    });

    this.openapi(removeFavoriteRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await removeFavoriteController.handle({promptId, ...body});
      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404 ||
        viewModel.status === 409
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const addTagRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/tags',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {
          content: {'application/json': {schema: z.object({tag: z.string()})}},
        },
      },
      responses: {
        200: {
          description: 'Tag added to prompt',
          content: {'application/json': {schema: z.object({promptId: z.string()})}},
        },
        401: { description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}} },
        403: { description: 'Not owner of prompt', content: {'application/json': {schema: z.object({error: z.string()})}} },
        404: { description: 'Prompt not found', content: {'application/json': {schema: z.object({error: z.string()})}} },
        409: { description: 'Tag already exists', content: {'application/json': {schema: z.object({error: z.string()})}} },
      },
    });

    this.openapi(addTagRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await addTagController.handle({promptId, ...body});
      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404 ||
        viewModel.status === 409
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const removeTagRoute = createRoute({
      method: 'delete',
      path: '/prompts/{id}/tags/{tag}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
          tag: z.string().openapi({ param: { name: 'tag', in: 'path' }, example: 'javascript' }),
        }),
        body: {content: {'application/json': {schema: z.object({})}}},
      },
      responses: {
        200: {
          description: 'Tag removed from prompt',
          content: {'application/json': {schema: z.object({promptId: z.string()})}},
        },
        401: { description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}} },
        403: { description: 'Not owner of prompt', content: {'application/json': {schema: z.object({error: z.string()})}} },
        404: { description: 'Prompt not found', content: {'application/json': {schema: z.object({error: z.string()})}} },
      },
    });

    this.openapi(removeTagRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId, tag } = c.req.valid('param');
      const viewModel = await removeTagController.handle({
        promptId,
        tag, ...body
      });
      if (
        viewModel.status === 401 ||
        viewModel.status === 403 ||
        viewModel.status === 404
      ) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });
  }
}
