import {create<PERSON>out<PERSON>, OpenAPIHono, z} from '@hono/zod-openapi';
import type {Container} from '@evyweb/ioctopus';
import {
  SearchPromptsByTextApiController
} from '@src/server/Discovery/presentation/api/SearchPromptsByText/SearchPromptsByTextApiController';
import {
  FilterPromptsByTagApiController
} from '@src/server/Discovery/presentation/api/FilterPromptsByTag/FilterPromptsByTagApiController';
import {
  FilterPromptsByTargetAIApiController
} from '@src/server/Discovery/presentation/api/FilterPromptsByTargetAI/FilterPromptsByTargetAIApiController';
import {
  FilterPromptsByAuthorApiController
} from '@src/server/Discovery/presentation/api/FilterPromptsByAuthor/FilterPromptsByAuthorApiController';
import {
  FilterPromptsByAuthorOrSharedApiController
} from '@src/server/Discovery/presentation/api/FilterPromptsByAuthorOrShared/FilterPromptsByAuthorOrSharedApiController';
import {
  ListPromptsByStatusApiController
} from '@src/server/Discovery/presentation/api/ListPromptsByStatus/ListPromptsByStatusApiController';
import {
  ListFavoritePromptsApiController
} from '@src/server/Discovery/presentation/api/ListFavoritePrompts/ListFavoritePromptsApiController';

const TAG = 'Discovery';

export class DiscoveryApp extends OpenAPIHono {
  private readonly container: Container;

  constructor(container: Container) {
    super();
    this.container = container;
    const searchByTextController = this.container.get<SearchPromptsByTextApiController>('SearchPromptsByTextController');
    const filterByTagController = this.container.get<FilterPromptsByTagApiController>('FilterPromptsByTagController');
    const filterByTargetAIController = this.container.get<FilterPromptsByTargetAIApiController>('FilterPromptsByTargetAIController');
    const filterByAuthorController = this.container.get<FilterPromptsByAuthorApiController>('FilterPromptsByAuthorController');
    const filterByAuthorOrSharedController = this.container.get<FilterPromptsByAuthorOrSharedApiController>('FilterPromptsByAuthorOrSharedController');
    const listByStatusController = this.container.get<ListPromptsByStatusApiController>('ListPromptsByStatusController');
    const listFavoritesController = this.container.get<ListFavoritePromptsApiController>('ListFavoritePromptsController');

    const searchByTextRoute = createRoute({
      method: 'get',
      path: '/prompts/search/{text}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          text: z.string().openapi({ param: { name: 'text', in: 'path' }, example: 'javascript' }),
        }),
      },
      responses: {
        200: {
          description: 'Prompts found by text',
          content: {
            'application/json': {
              schema: z.object({
                prompts: z.array(
                  z.object({
                    id: z.string(),
                    content: z.string(),
                    targetAI: z.string(),
                    tags: z.array(z.string()),
                    authorId: z.string(),
                    status: z.enum(['draft', 'published', 'archived', 'shared']),
                    sharedWith: z.array(z.string()),
                    favoritedBy: z.array(z.string()),
                  }),
                ),
              }),
            },
          },
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(searchByTextRoute, async (c) => {
      const { text } = c.req.valid('param');
      const viewModel = await searchByTextController.handle({text});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const filterByTagRoute = createRoute({
      method: 'get',
      path: '/prompts/tags/{tag}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          tag: z.string().openapi({ param: { name: 'tag', in: 'path' }, example: 'javascript' }),
        }),
      },
      responses: {
        200: {
          description: 'Prompts filtered by tag',
          content: {
            'application/json': {
              schema: z.object({
                prompts: z.array(z.object({
                  id: z.string(),
                  content: z.string(),
                  targetAI: z.string(),
                  tags: z.array(z.string()),
                  authorId: z.string(),
                  status: z.enum(['draft', 'published', 'archived', 'shared']),
                  sharedWith: z.array(z.string()),
                  favoritedBy: z.array(z.string()),
                }))
              })
            }
          },
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(filterByTagRoute, async (c) => {
      const { tag } = c.req.valid('param');
      const viewModel = await filterByTagController.handle({tag});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const filterByTargetAIRoute = createRoute({
      method: 'get',
      path: '/prompts/target-ai/{targetAI}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          targetAI: z.string().openapi({ param: { name: 'targetAI', in: 'path' }, example: 'GPT-4' }),
        }),
      },
      responses: {
        200: {
          description: 'Prompts filtered by target AI',
          content: {
            'application/json': {
              schema: z.object({
                prompts: z.array(z.object({
                  id: z.string(),
                  content: z.string(),
                  targetAI: z.string(),
                  tags: z.array(z.string()),
                  authorId: z.string(),
                  status: z.enum(['draft', 'published', 'archived', 'shared']),
                  sharedWith: z.array(z.string()),
                  favoritedBy: z.array(z.string()),
                }))
              })
            }
          },
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(filterByTargetAIRoute, async (c) => {
      const { targetAI } = c.req.valid('param');
      const viewModel = await filterByTargetAIController.handle({targetAI});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const filterByAuthorRoute = createRoute({
      method: 'get',
      path: '/prompts/authors/{authorId}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          authorId: z.string().openapi({ param: { name: 'authorId', in: 'path' }, example: 'user-123' }),
        }),
      },
      responses: {
        200: {
          description: 'Prompts filtered by author',
          content: {
            'application/json': {
              schema: z.object({
                prompts: z.array(z.object({
                  id: z.string(),
                  content: z.string(),
                  targetAI: z.string(),
                  tags: z.array(z.string()),
                  authorId: z.string(),
                  status: z.enum(['draft', 'published', 'archived', 'shared']),
                  sharedWith: z.array(z.string()),
                  favoritedBy: z.array(z.string()),
                }))
              })
            }
          },
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(filterByAuthorRoute, async (c) => {
      const { authorId } = c.req.valid('param');
      const viewModel = await filterByAuthorController.handle({authorId});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const filterByAuthorOrSharedRoute = createRoute({
      method: 'get',
      path: '/prompts/authors/{authorId}/shared-with/{userId}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          authorId: z.string().openapi({ param: { name: 'authorId', in: 'path' }, example: 'user-123' }),
          userId: z.string().openapi({ param: { name: 'userId', in: 'path' }, example: 'user-456' }),
        }),
      },
      responses: {
        200: {
          description: 'Prompts filtered by author or shared with user',
          content: {
            'application/json': {
              schema: z.object({
                prompts: z.array(z.object({
                  id: z.string(),
                  content: z.string(),
                  targetAI: z.string(),
                  tags: z.array(z.string()),
                  authorId: z.string(),
                  status: z.enum(['draft', 'published', 'archived', 'shared']),
                  sharedWith: z.array(z.string()),
                  favoritedBy: z.array(z.string()),
                }))
              })
            }
          },
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(filterByAuthorOrSharedRoute, async (c) => {
      const { authorId } = c.req.valid('param');
      const viewModel = await filterByAuthorOrSharedController.handle({
        authorId
      });
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const listByStatusRoute = createRoute({
      method: 'get',
      path: '/prompts/status/{status}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          status: z.enum(['draft', 'published', 'archived', 'shared']).openapi({ param: { name: 'status', in: 'path' }, example: 'published' }),
        }),
      },
      responses: {
        200: {
          description: 'Prompts listed by status',
          content: {
            'application/json': {
              schema: z.object({
                prompts: z.array(z.object({
                  id: z.string(),
                  content: z.string(),
                  targetAI: z.string(),
                  tags: z.array(z.string()),
                  authorId: z.string(),
                  status: z.enum(['draft', 'published', 'archived', 'shared']),
                  sharedWith: z.array(z.string()),
                  favoritedBy: z.array(z.string()),
                }))
              })
            }
          },
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(listByStatusRoute, async (c) => {
      const { status } = c.req.valid('param');
      const viewModel = await listByStatusController.handle({status});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const listFavoritesRoute = createRoute({
      method: 'get',
      path: '/prompts/favorites/{userId}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          userId: z.string().openapi({ param: { name: 'userId', in: 'path' }, example: 'user-123' }),
        }),
      },
      responses: {
        200: {
          description: 'Favorite prompts listed by user',
          content: {
            'application/json': {
              schema: z.object({
                prompts: z.array(z.object({
                  id: z.string(),
                  content: z.string(),
                  targetAI: z.string(),
                  tags: z.array(z.string()),
                  authorId: z.string(),
                  status: z.enum(['draft', 'published', 'archived', 'shared']),
                  sharedWith: z.array(z.string()),
                  favoritedBy: z.array(z.string()),
                }))
              })
            }
          },
        },
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(listFavoritesRoute, async (c) => {
      const { userId } = c.req.valid('param');
      const viewModel = await listFavoritesController.handle();
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });
  }
}
