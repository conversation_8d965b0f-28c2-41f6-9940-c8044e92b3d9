import {<PERSON><PERSON><PERSON><PERSON><PERSON>, create<PERSON>out<PERSON>, z} from '@hono/zod-openapi';
import type {StatusCode} from 'hono/utils/http-status';
import type {Container} from '@evyweb/ioctopus';
import {
  SetPromptVisibilityApiController
} from '@src/server/SharingAndVisibility/presentation/api/SetPromptVisibility/SetPromptVisibilityApiController';
import {
  SharePromptWithUsersApiController
} from '@src/server/SharingAndVisibility/presentation/api/SharePromptWithUsers/SharePromptWithUsersApiController';
import {
  SharePromptByLinkApiController
} from '@src/server/SharingAndVisibility/presentation/api/SharePromptByLink/SharePromptByLinkApiController';
import {
  ViewPromptAccessListApiController
} from '@src/server/SharingAndVisibility/presentation/api/ViewPromptAccessList/ViewPromptAccessListApiController';
import {
  RemoveUserAccessFromPromptApiController
} from '@src/server/SharingAndVisibility/presentation/api/RemoveUserAccessFromPrompt/RemoveUserAccessFromPromptApiController';

const TAG = 'SharingAndVisibility';

export class SharingAndVisibilityApp extends OpenAPIHono {
  private readonly container: Container;

  constructor(container: Container) {
    super();
    this.container = container;
    const setVisibilityController = this.container.get<SetPromptVisibilityApiController>('SetPromptVisibilityController');
    const shareWithUsersController = this.container.get<SharePromptWithUsersApiController>('SharePromptWithUsersController');
    const shareByLinkController = this.container.get<SharePromptByLinkApiController>('SharePromptByLinkController');
    const viewAccessController = this.container.get<ViewPromptAccessListApiController>('ViewPromptAccessListController');
    const removeUserAccessController = this.container.get<RemoveUserAccessFromPromptApiController>('RemoveUserAccessFromPromptController');

    const setVisibilityRoute = createRoute({
      method: 'put',
      path: '/prompts/{id}/visibility',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {content: {'application/json': {schema: z.object({visibility: z.enum(['private', 'public', 'link'])})}}}
      },
      responses: {
        200: {
          description: 'Prompt visibility set',
          content: {
            'application/json': {
              schema: z.object({
                promptId: z.string(),
                visibility: z.enum(['private', 'public', 'link'])
              })
            }
          }
        },
        401: {description: 'Unauthorized', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(setVisibilityRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await setVisibilityController.handle({promptId, ...body});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const shareByLinkRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/share/link',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
      },
      responses: {
        200: {
          description: 'Prompt shared by link',
          content: {'application/json': {schema: z.object({promptId: z.string(), shareLink: z.string()})}}
        },
        401: {description: 'Unauthorized', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(shareByLinkRoute, async (c) => {
      const { id: promptId } = c.req.valid('param');
      const viewModel = await shareByLinkController.handle({promptId});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const shareWithUsersRoute = createRoute({
      method: 'post',
      path: '/prompts/{id}/share/users',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
        body: {content: {'application/json': {schema: z.object({userIds: z.array(z.string())})}}}
      },
      responses: {
        200: {
          description: 'Prompt shared with users',
          content: {'application/json': {schema: z.object({promptId: z.string(), userIds: z.array(z.string())})}}
        },
        401: {description: 'Unauthorized', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(shareWithUsersRoute, async (c) => {
      const body = c.req.valid('json');
      const { id: promptId } = c.req.valid('param');
      const viewModel = await shareWithUsersController.handle({promptId, ...body});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const removeUserAccessRoute = createRoute({
      method: 'delete',
      path: '/prompts/{id}/share/users/{userId}',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
          userId: z.string().openapi({ param: { name: 'userId', in: 'path' }, example: 'user-456' }),
        }),
      },
      responses: {
        200: {
          description: 'User access removed from prompt',
          content: {'application/json': {schema: z.object({promptId: z.string(), userIds: z.array(z.string())})}}
        },
        401: {description: 'Unauthorized', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(removeUserAccessRoute, async (c) => {
      const { id: promptId, userId } = c.req.valid('param');
      const viewModel = await removeUserAccessController.handle({
        promptId,
        targetUserId: userId
      });
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });

    const viewAccessRoute = createRoute({
      method: 'get',
      path: '/prompts/{id}/access',
      tags: [TAG],
      security: [{ bearerAuth: [] }],
      request: {
        params: z.object({
          id: z.string().openapi({ param: { name: 'id', in: 'path' }, example: 'prompt-123' }),
        }),
      },
      responses: {
        200: {
          description: 'Prompt access list',
          content: {
            'application/json': {
              schema: z.object({
                promptId: z.string(),
                visibility: z.enum(['private', 'public', 'link']),
                userIds: z.array(z.string()),
                shareLink: z.string().nullable()
              })
            }
          }
        },
        401: {description: 'Unauthorized', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(viewAccessRoute, async (c) => {
      const { id: promptId } = c.req.valid('param');
      const viewModel = await viewAccessController.handle({promptId});
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }

      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }

      throw new Error('Unexpected error');
    });
  }
}
