import {createRoute, OpenAPIH<PERSON>, z} from '@hono/zod-openapi';
import type {Container} from '@evyweb/ioctopus';
import {SignInApiController} from '@src/server/Authentication/presentation/api/SignIn/SignInApiController';
import {RegisterApiController} from '@src/server/Authentication/presentation/api/Register/RegisterApiController';
import {SignOutApiController} from '@src/server/Authentication/presentation/api/SignOut/SignOutApiController';

const TAG = 'Authentication';

export class AuthenticationApp extends OpenAPIHono {
  private readonly container: Container;

  constructor(container: Container) {
    super();
    this.container = container;
    const signInController = this.container.get<SignInApiController>('SignInController');
    const registerController = this.container.get<RegisterApiController>('RegisterController');
    const signOutController = this.container.get<SignOutApiController>('SignOutController');

    const signInRoute = createRoute({
      method: 'post',
      path: '/auth/sign-in',
      tags: [TAG],
      security: [],
      request: {
        body: {
          content: {
            'application/json': {schema: z.object({email: z.string(), password: z.string()})},
          },
        },
      },
      responses: {
        200: {
          description: 'User signed in',
          content: {
            'application/json': {schema: z.object({userId: z.string(), token: z.string()})},
          },
        },
        401: {description: 'Invalid credentials', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(signInRoute, async (c) => {
      const body = c.req.valid('json');
      const viewModel = await signInController.handle(body);
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }
      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }
      throw new Error('Unexpected error');
    });

    const registerRoute = createRoute({
      method: 'post',
      path: '/auth/register',
      tags: [TAG],
      security: [],
      request: {
        body: {
          content: {
            'application/json': {schema: z.object({email: z.string(), password: z.string()})},
          },
        },
      },
      responses: {
        201: {description: 'User registered', content: {'application/json': {schema: z.object({userId: z.string()})}}},
      },
    });

    this.openapi(registerRoute, async (c) => {
      const body = c.req.valid('json');
      const viewModel = await registerController.handle(body);
      if (viewModel.status === 201) {
        return c.json(viewModel.body, viewModel.status);
      }
      throw new Error('Unexpected error');
    });

    const signOutRoute = createRoute({
      method: 'post',
      path: '/auth/sign-out',
      tags: [TAG],
      responses: {
        200: {description: 'User signed out', content: {'application/json': {schema: z.object({userId: z.string()})}}},
        401: {description: 'Not authenticated', content: {'application/json': {schema: z.object({error: z.string()})}}},
      },
    });

    this.openapi(signOutRoute, async (c) => {
      const viewModel = await signOutController.handle();
      if (viewModel.status === 401) {
        return c.json(viewModel.body, viewModel.status);
      }
      if (viewModel.status === 200) {
        return c.json(viewModel.body, viewModel.status);
      }
      throw new Error('Unexpected error');
    });
  }
}
