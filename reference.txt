// ==============================
// File: app/App.tsx (single-file preview)
// NOTE: This canvas only supports one file preview. I've refactored the UI
// into clearly separated components with file-style sections. You can copy each
// section into its own file in a real project (e.g., /components/Header.tsx, /components/PromptCard.tsx, etc.).
// ==============================

import React, { useEffect, useState, useRef, forwardRef } from "react"
import {
  Search,
  Sparkles,
  Download,
  Upload,
  Plus,
  Folder,
  FolderOpen,
  FileText,
  ChevronRight,
  ChevronDown,
  Star,
  Archive,
  Copy,
  Share2,
  Trash2,
  FilePenLine,
  MoreVertical,
  Send,
  Menu,
  User,
  LogOut,
  Settings,
  Check,
} from "lucide-react"
import { PanelGroup as ResizablePanelGroup, Panel as ResizablePanel, PanelResizeH<PERSON>le as Resi<PERSON><PERSON><PERSON><PERSON> } from "react-resizable-panels"

/******************************
 * File: components/ScrollArea.tsx
 ******************************/
function ScrollArea({ children, className = "" }: { children: React.ReactNode; className?: string }) {
  return <div className={`h-full overflow-auto ${className}`}>{children}</div>
}

/******************************
 * File: components/ThemeStyles.tsx
 ******************************/
function ThemeStyles() {
  return (
    <style>{`
:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3211 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3211 0 0);
  --primary: oklch(0.6231 0.1880 259.8145);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9670 0.0029 264.5419);
  --secondary-foreground: oklch(0.4461 0.0263 256.8018);
  --muted: oklch(0.9846 0.0017 247.8389);
  --muted-foreground: oklch(0.5510 0.0234 264.3637);
  --accent: oklch(0.9514 0.0250 236.8242);
  --accent-foreground: oklch(0.3791 0.1378 265.5222);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9276 0.0058 264.5313);
  --input: oklch(0.9276 0.0058 264.5313);
  --ring: oklch(0.6231 0.1880 259.8145);
  --chart-1: oklch(0.6231 0.1880 259.8145);
  --chart-2: oklch(0.5461 0.2152 262.8809);
  --chart-3: oklch(0.4882 0.2172 264.3763);
  --chart-4: oklch(0.4244 0.1809 265.6377);
  --chart-5: oklch(0.3791 0.1378 265.5222);
  --sidebar: oklch(0.9846 0.0017 247.8389);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.6231 0.1880 259.8145);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9514 0.0250 236.8242);
  --sidebar-accent-foreground: oklch(0.3791 0.1378 265.5222);
  --sidebar-border: oklch(0.9276 0.0058 264.5313);
  --sidebar-ring: oklch(0.6231 0.1880 259.8145);
  --border-strong: oklch(0.86 0.005 264.5);
  --row-odd: oklch(0.985 0 0);
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}
.dark {
  --background: oklch(0.2046 0 0);
  --foreground: oklch(0.9219 0 0);
  --card: oklch(0.2686 0 0);
  --card-foreground: oklch(0.9219 0 0);
  --popover: oklch(0.2686 0 0);
  --popover-foreground: oklch(0.9219 0 0);
  --primary: oklch(0.6231 0.1880 259.8145);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.2686 0 0);
  --secondary-foreground: oklch(0.9219 0 0);
  --muted: oklch(0.2686 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.3791 0.1378 265.5222);
  --accent-foreground: oklch(0.8823 0.0571 254.1284);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3715 0 0);
  --input: oklch(0.3715 0 0);
  --ring: oklch(0.6231 0.1880 259.8145);
  --chart-1: oklch(0.7137 0.1434 254.6240);
  --chart-2: oklch(0.6231 0.1880 259.8145);
  --chart-3: oklch(0.5461 0.2152 262.8809);
  --chart-4: oklch(0.4882 0.2172 264.3763);
  --chart-5: oklch(0.4244 0.1809 265.6377);
  --sidebar: oklch(0.2046 0 0);
  --sidebar-foreground: oklch(0.9219 0 0);
  --sidebar-primary: oklch(0.6231 0.1880 259.8145);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.3791 0.1378 265.5222);
  --sidebar-accent-foreground: oklch(0.8823 0.0571 254.1284);
  --sidebar-border: oklch(0.3715 0 0);
  --sidebar-ring: oklch(0.6231 0.1880 259.8145);
  --border-strong: oklch(0.30 0 0);
  --row-odd: oklch(0.24 0 0);
}
html, body, #root, #__next { background-color: var(--background); color: var(--foreground) }
* { letter-spacing: var(--tracking-normal) }
.border, .border-b, .border-l, .border-r, .border-t { border-color: var(--border) }
.border-strong { border-color: var(--border-strong) }
.bg-background { background-color: var(--background) }
.bg-card { background-color: var(--card) }
.bg-popover { background-color: var(--popover) }
.bg-accent { background-color: var(--accent) }
.bg-secondary { background-color: var(--secondary) }
.text-muted-foreground { color: var(--muted-foreground) }
.text-secondary-foreground { color: var(--secondary-foreground) }
.text-primary-foreground { color: var(--primary-foreground) }
.bg-primary { background-color: var(--primary) }
.focus\\:ring-ring:focus { box-shadow: 0 0 0 2px var(--ring) }
.rounded-md { border-radius: var(--radius) }
.shadow-sm { box-shadow: var(--shadow-sm) }
.shadow-xl { box-shadow: var(--shadow-xl) }
.shadow-md { box-shadow: var(--shadow-md) }
.shadow-lg { box-shadow: var(--shadow-lg) }
.shadow-2xl { box-shadow: var(--shadow-2xl) }
.sr-only { position:absolute; width:1px; height:1px; padding:0; margin:-1px; overflow:hidden; clip:rect(0,0,0,0); white-space:nowrap; border:0 }

/* Badges (color-meaningful, based on theme tokens) */
.badge { display:inline-flex; align-items:center; gap:0.25rem; padding:0.125rem 0.5rem; border-radius:9999px; font-size:10px; line-height:1; border:1px solid var(--border-strong); }
.badge-tag { background: transparent; color: inherit; }
.badge-outline { background: transparent; color: var(--muted-foreground); }
.badge-favorite { background: var(--accent); color: var(--accent-foreground); border-color: transparent; }
/* Model is important: use a dedicated, theme-matched chart color */
.badge-model { background: var(--chart-2); color: var(--primary-foreground); border-color: transparent; font-weight: 600; }
.badge-status-published { background: var(--primary); color: var(--primary-foreground); border-color: transparent; }
.badge-status-draft { background: var(--muted); color: var(--muted-foreground); border-color: transparent; }

/* Prompt list row states */
.prompt-card { background: var(--card); transition: background-color .15s ease, color .15s ease, border-color .15s ease, opacity .15s ease; }
.prompt-card.is-odd { background: var(--row-odd); }
/* Selected: border only (no background or text color changes) */
.prompt-card.is-selected { border-color: var(--ring); }
/* Checked: keep original style, red border */
.prompt-card.is-checked { border-color: var(--destructive); }
/* If both selected & checked, red wins */
.prompt-card.is-selected.is-checked { border-color: var(--destructive); }
/* Dim non-checked cards when at least one is checked */
.prompt-card.is-dimmed { opacity: .45; }
`}</style>
  )
}

/******************************
 * File: components/Header.tsx
 ******************************/
interface HeaderProps {
  dark: boolean
  setDark: (v: boolean) => void
  settingsOpen: boolean
  setSettingsOpen: (v: boolean) => void
  userOpen: boolean
  setUserOpen: (v: boolean) => void
  viewOpen: boolean
  setViewOpen: (v: boolean) => void
  modelOpen: boolean
  setModelOpen: (v: boolean) => void
  currentView: string
  setCurrentView: (v: string) => void
  currentModel: string
  setCurrentModel: (v: string) => void
  viewItems: string[]
  modelItems: string[]
  viewRef: React.RefObject<HTMLDivElement>
  modelRef: React.RefObject<HTMLDivElement>
  settingsRef: React.RefObject<HTMLDivElement>
  userRef: React.RefObject<HTMLDivElement>
}

function Header({
  dark,
  setDark,
  settingsOpen,
  setSettingsOpen,
  userOpen,
  setUserOpen,
  viewOpen,
  setViewOpen,
  modelOpen,
  setModelOpen,
  currentView,
  setCurrentView,
  currentModel,
  setCurrentModel,
  viewItems,
  modelItems,
  viewRef,
  modelRef,
  settingsRef,
  userRef,
}: HeaderProps) {
  return (
    <header className="sticky top-0 z-40 border-b border-strong bg-background/80 backdrop-blur">
      <div className="mx-auto p-2 md:p-2">
        {/* Row 1: brand left, menus right */}
        <div className="flex items-center gap-2">
          <button aria-label="Open folders" onClick={() => console.log('open folders')} className="md:hidden inline-flex h-8 w-8 items-center justify-center rounded-md border border-strong"><Menu size={16} /></button>
          <div className="flex items-center gap-2 font-semibold"><Sparkles size={18} /> Prompt Manager</div>
          <div className="ml-auto flex items-center gap-2">
            <div className="relative" ref={settingsRef}>
              <button aria-label="Settings" onClick={() => setSettingsOpen(!settingsOpen)} className="inline-flex h-9 w-9 items-center justify-center rounded-md border border-strong bg-background">
                <Settings size={16} />
              </button>
              {settingsOpen && (
                <div className="absolute right-0 z-20 mt-2 w-64 rounded-md border border-strong bg-popover shadow-lg">
                  <div className="px-3 py-2 text-xs text-muted-foreground">Settings</div>
                  <ul role="menu" className="p-1">
                    <li className="px-2 py-1 text-xs text-muted-foreground">Theme</li>
                    <li>
                      <button className="flex h-9 w-full items-center justify-between rounded px-2 text-sm hover:bg-accent" onClick={()=> setDark(true)}>
                        Dark {dark && <Check size={16} />}
                      </button>
                    </li>
                    <li>
                      <button className="flex h-9 w-full items-center justify-between rounded px-2 text-sm hover:bg-accent" onClick={()=> setDark(false)}>
                        Light {!dark && <Check size={16} />}
                      </button>
                    </li>
                    <li className="my-1 h-px bg-[var(--border-strong)]" />
                    <li>
                      <button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={()=> console.log('import')}>
                        <Upload size={16}/> Import
                      </button>
                    </li>
                    <li>
                      <button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={()=> console.log('export')}>
                        <Download size={16}/> Export
                      </button>
                    </li>
                  </ul>
                </div>
              )}
            </div>
            <div className="relative" ref={userRef}>
              <button aria-label="User menu" onClick={() => setUserOpen(!userOpen)} className="inline-flex h-9 items-center gap-1 rounded-md border border-strong bg-background px-1.5">
                <div className="h-7 w-7 rounded-lg overflow-hidden grid place-items-center bg-gradient-to-br from-[var(--chart-3)] to-[var(--chart-1)] text-[10px] font-semibold">JD</div>
                <ChevronDown size={14} className={userOpen ? 'rotate-180 transition-transform' : 'transition-transform'} />
              </button>
              {userOpen && (
                <div className="absolute right-0 z-20 mt-2 w-56 rounded-md border border-strong bg-popover shadow-lg">
                  <div className="px-3 py-2 text-xs text-muted-foreground">Signed in as <span className="font-medium text-foreground">john.doe</span></div>
                  <ul role="menu" className="p-1">
                    <li>
                      <button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={() => console.log('open profile')}>
                        <User size={16}/> Profile
                      </button>
                    </li>
                    <li>
                      <button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent text-destructive-foreground" onClick={() => console.log('logout')}>
                        <LogOut size={16}/> Log out
                      </button>
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Row 2: filters & search (desktop), stacked on mobile */}
        <DesktopFilters
          viewRef={viewRef}
          modelRef={modelRef}
          viewOpen={viewOpen}
          setViewOpen={setViewOpen}
          modelOpen={modelOpen}
          setModelOpen={setModelOpen}
          currentView={currentView}
          setCurrentView={setCurrentView}
          currentModel={currentModel}
          setCurrentModel={setCurrentModel}
          viewItems={viewItems}
          modelItems={modelItems}
        />
        <MobileControls
          viewRef={viewRef}
          modelRef={modelRef}
          viewOpen={viewOpen}
          setViewOpen={setViewOpen}
          modelOpen={modelOpen}
          setModelOpen={setModelOpen}
          currentView={currentView}
          setCurrentView={setCurrentView}
          currentModel={currentModel}
          setCurrentModel={setCurrentModel}
          viewItems={viewItems}
          modelItems={modelItems}
        />
      </div>
    </header>
  )
}

/******************************
 * File: components/DesktopFilters.tsx
 ******************************/
function DesktopFilters(props: any) {
  const { viewRef, modelRef, viewOpen, setViewOpen, modelOpen, setModelOpen, currentView, setCurrentView, currentModel, setCurrentModel, viewItems, modelItems } = props
  return (
    <div className="mt-2 hidden min-w-0 items-center gap-2 md:flex">
      <div className="relative w-[180px] flex-none" ref={viewRef}>
        <button className="inline-flex h-9 w-full items-center justify-between gap-2 rounded-md border border-strong bg-background px-3 text-sm" onClick={() => setViewOpen(!viewOpen)} aria-haspopup="menu" aria-expanded={viewOpen}>
          {currentView}
          <ChevronDown size={16}/>
        </button>
        {viewOpen && (
          <div className="absolute right-0 z-10 mt-1 w-full rounded-md border border-strong bg-popover shadow-lg">
            <ul role="menu" className="p-1">
              {viewItems.map((v: string) => (
                <li key={v}><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={() => { setCurrentView(v); setViewOpen(false) }}>{v}</button></li>
              ))}
            </ul>
          </div>
        )}
      </div>
      <div className="relative w-[220px] flex-none" ref={modelRef}>
        <button className="inline-flex h-9 w-full items-center justify-between gap-2 rounded-md border border-strong bg-background px-3 text-sm" onClick={() => setModelOpen(!modelOpen)} aria-haspopup="menu" aria-expanded={modelOpen}>
          {currentModel}
          <ChevronDown size={16}/>
        </button>
        {modelOpen && (
          <div className="absolute right-0 z-10 mt-1 w-full rounded-md border border-strong bg-popover shadow-lg">
            <ul role="menu" className="p-1">
              {modelItems.map((m: string) => (
                <li key={m}><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={() => { setCurrentModel(m); setModelOpen(false) }}>{m}</button></li>
              ))}
            </ul>
          </div>
        )}
      </div>
      <div className="relative min-w-[200px] flex-1">
        <Search className="absolute left-2 top-2" size={16} />
        <label htmlFor="search-desktop" className="sr-only">Search prompts</label>
        <input id="search-desktop" className="h-9 w-full rounded-md border border-strong bg-background pl-8 pr-3 text-sm outline-none focus:ring-ring" placeholder="Search prompts" />
      </div>
    </div>
  )
}

/******************************
 * File: components/MobileControls.tsx
 ******************************/
function MobileControls(props: any) {
  const { viewRef, modelRef, viewOpen, setViewOpen, modelOpen, setModelOpen, currentView, setCurrentView, currentModel, setCurrentModel, viewItems, modelItems } = props
  return (
    <div className="mt-2 w-full md:hidden">
      <div className="relative">
        <Search className="absolute left-2 top-2" size={16} />
        <label htmlFor="search" className="sr-only">Search prompts</label>
        <input id="search" className="h-9 w-full rounded-md border border-strong bg-background pl-8 pr-3 text-sm outline-none focus:ring-ring" placeholder="Search prompts" />
      </div>
      <div className="mt-2 grid grid-cols-2 gap-2">
        <div className="relative" ref={viewRef}>
          <button className="inline-flex h-9 w-full items-center justify-between gap-2 rounded-md border border-strong bg-background px-3 text-sm" onClick={() => setViewOpen(!viewOpen)} aria-haspopup="menu" aria-expanded={viewOpen}>
            {currentView}
            <ChevronDown size={16}/>
          </button>
          {viewOpen && (
            <div className="absolute right-0 z-10 mt-1 w-full rounded-md border border-strong bg-popover shadow-lg">
              <ul role="menu" className="p-1">
                {viewItems.map((v: string) => (
                  <li key={v}><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={() => { setCurrentView(v); setViewOpen(false) }}>{v}</button></li>
                ))}
              </ul>
            </div>
          )}
        </div>
        <div className="relative" ref={modelRef}>
          <button className="inline-flex h-9 w-full items-center justify-between gap-2 rounded-md border border-strong bg-background px-3 text-sm" onClick={() => setModelOpen(!modelOpen)} aria-haspopup="menu" aria-expanded={modelOpen}>
            {currentModel}
            <ChevronDown size={16}/>
          </button>
          {modelOpen && (
            <div className="absolute right-0 z-10 mt-1 w-full rounded-md border border-strong bg-popover shadow-lg">
              <ul role="menu" className="p-1">
                {modelItems.map((m: string) => (
                  <li key={m}><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={() => { setCurrentModel(m); setModelOpen(false) }}>{m}</button></li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

/******************************
 * File: components/Explorer.tsx
 ******************************/
function ExplorerRow({ icon, name, count, openKey, explorerOpen, setExplorerOpen, nested }: any) {
  return (
    <div className={`group relative flex items-center gap-2 rounded px-2 py-1 text-sm hover:bg-accent ${nested ? 'pl-6' : ''}`}>
      {icon}
      <span className="flex-1 truncate">{name}</span>
      {typeof count === 'number' && <span className="text-xs text-muted-foreground">{count}</span>}
      <button data-explorer-kebab onClick={(e)=>{ e.stopPropagation(); setExplorerOpen(explorerOpen===openKey?null:openKey) }} className={`opacity-0 group-hover:opacity-100 transition-opacity grid h-7 w-7 place-items-center rounded-md border border-strong ${explorerOpen===openKey ? 'opacity-100' : ''}`}><MoreVertical size={14}/></button>
      <div data-explorer-menu-root className="absolute right-2 top-8 z-10 w-44 rounded-md border border-strong bg-popover shadow-lg" style={{ display: explorerOpen===openKey ? 'block' : 'none' }}>
        <ul className="p-1">
          <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent">Create folder</button></li>
          <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent">Rename folder</button></li>
          <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent text-destructive-foreground">Delete folder</button></li>
        </ul>
      </div>
    </div>
  )
}

function Explorer({ explorerOpen, setExplorerOpen }: { explorerOpen: string | null; setExplorerOpen: (v: any)=>void }) {
  return (
    <aside className="h-full border-r border-strong overflow-hidden" style={{ backgroundColor: "var(--sidebar)", color: "var(--sidebar-foreground)" }}>
      <ScrollArea className="h-full">
        <div className="p-3 md:p-4">
          <div className="mb-2 text-xs font-medium uppercase tracking-wide text-muted-foreground">Explorer</div>
          <div className="space-y-1">
            <div>
              <div className="flex items-center gap-2 text-sm">
                <ChevronDown size={14}/> <FolderOpen size={16}/> <span className="flex-1 truncate">Branding</span> <span className="text-xs text-muted-foreground">12</span>
                {/* kebab is inside row component for consistency */}
              </div>
              <div className="mt-1 space-y-1">
                <ExplorerRow icon={<FileText size={16}/>} name="Launch" openKey="file-launch" explorerOpen={explorerOpen} setExplorerOpen={setExplorerOpen} nested />
                <ExplorerRow icon={<FileText size={16}/>} name="Taglines" openKey="file-taglines" explorerOpen={explorerOpen} setExplorerOpen={setExplorerOpen} nested />
              </div>
            </div>
            <ExplorerRow icon={<><ChevronRight size={14}/> <Folder size={16}/></>} name="Summaries" count={7} openKey="folder-summaries" explorerOpen={explorerOpen} setExplorerOpen={setExplorerOpen} />
            <ExplorerRow icon={<><ChevronRight size={14}/> <Folder size={16}/></>} name="QA" count={5} openKey="folder-qa" explorerOpen={explorerOpen} setExplorerOpen={setExplorerOpen} />
          </div>
        </div>
      </ScrollArea>
    </aside>
  )
}

/******************************
 * File: components/PromptCard.tsx
 ******************************/
interface PromptCardProps {
  id: number
  index: number
  title: string
  preview: string
  tags: string[]
  model: string
  isDraft?: boolean
  simpleDate: string
  selectedId: number | null
  setSelectedId: (id: number)=>void
  checkedIds: Set<number>
  toggleChecked: (id: number)=>void
  cardMenuOpen: number | null
  setCardMenuOpen: (id: number | null)=>void
}

function PromptCard({ id, index, title, preview, tags, model, isDraft, simpleDate, selectedId, setSelectedId, checkedIds, toggleChecked, cardMenuOpen, setCardMenuOpen }: PromptCardProps) {
  const isChecked = checkedIds.has(id)
  const isOdd = index % 2 === 1
  const isSelected = selectedId === id
  const stateClass = isChecked ? 'is-checked' : isSelected ? 'is-selected' : isOdd ? 'is-odd' : ''
  const isDimmed = checkedIds.size > 0 && !isChecked
  return (
    <div
      className={`prompt-card rounded-xl border border-strong shadow-sm ${stateClass} ${isDimmed ? 'is-dimmed' : ''}`}
      role="button"
      tabIndex={0}
      onClick={() => setSelectedId(id)}
      onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') setSelectedId(id) }}
    >
      <div className="p-3">
        {/* Line 1: checkbox + title + date + row menu */}
        <div className="flex items-center gap-3">
          <input type="checkbox" className="mt-0.5" checked={isChecked} onClick={(e)=> e.stopPropagation()} onChange={(e)=> { e.stopPropagation(); toggleChecked(id) }} />
          <span className="text-sm font-semibold">{title}</span>
          <div className="ml-auto relative flex items-center gap-1">
            <span className="badge badge-outline">{simpleDate}</span>
            <button data-card-kebab aria-label="Row actions" className="grid h-7 w-7 place-items-center rounded-md border border-strong hover:bg-accent" onClick={(e)=>{ e.stopPropagation(); setCardMenuOpen(cardMenuOpen===id?null:id) }}>
              <MoreVertical size={14}/>
            </button>
            <div data-card-menu-root className="absolute right-0 top-8 z-10 w-48 rounded-md border border-strong bg-popover shadow-lg" style={{ display: cardMenuOpen===id ? 'block' : 'none' }} onClick={(e)=> e.stopPropagation()}>
              <ul className="p-1">
                <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={()=> console.log('favorite', id)}><Star size={16}/> Add to favorites</button></li>
                <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={()=> console.log('edit', id)}><FilePenLine size={16}/> Edit</button></li>
                <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={()=> console.log('duplicate', id)}><Copy size={16}/> Duplicate</button></li>
                <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent" onClick={()=> console.log('share', id)}><Share2 size={16}/> Share</button></li>
                <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent text-destructive-foreground" onClick={()=> console.log('delete', id)}><Trash2 size={16}/> Delete</button></li>
              </ul>
            </div>
          </div>
        </div>
        {/* Line 2: meta */}
        <div className="mt-2 flex flex-wrap items-center gap-2">
          <span className="badge badge-model">{model}</span>
          {index === 1 && <span className="badge badge-favorite">★ Favorite</span>}
          <span className={`badge ${isDraft ? 'badge-status-draft' : 'badge-status-published'}`}>{isDraft ? 'draft' : 'published'}</span>
          <div className="ml-auto hidden sm:flex gap-1">
            <button className="h-9 w-9 rounded-md hover:bg-accent grid place-items-center"><Star size={16}/></button>
            <button className="h-9 w-9 rounded-md hover:bg-accent grid place-items-center"><Copy size={16}/></button>
            <button className="h-9 w-9 rounded-md hover:bg-accent grid place-items-center"><Share2 size={16}/></button>
            <button className="h-9 w-9 rounded-md hover:bg-accent grid place-items-center"><Archive size={16}/></button>
            <button className="h-9 w-9 rounded-md hover:bg-accent grid place-items-center"><Trash2 size={16}/></button>
          </div>
        </div>
        <div className="mt-2 line-clamp-2 text-xs text-muted-foreground">{preview}</div>
        <div className="mt-2 flex flex-wrap gap-1.5">
          {tags.map((t) => <span key={t} className="badge badge-tag">#{t}</span>)}
        </div>
      </div>
    </div>
  )
}

/******************************
 * File: components/RightPanel.tsx
 ******************************/
function RightPanel({ menuRef, menuOpen, setMenuOpen }: { menuRef: React.RefObject<HTMLDivElement>; menuOpen: boolean; setMenuOpen: (v: boolean)=>void }) {
  return (
    <section className="h-full border-l border-strong overflow-hidden">
      <ScrollArea className="h-full p-4">
        <div className="flex h-full flex-col">
          <div className="flex items-center gap-2">
            <div className="relative min-w-[160px]"><button className="inline-flex h-10 w-full items-center justify-between rounded-md border border-strong bg-background px-3 text-sm">GPT‑4o<ChevronDown size={16}/></button></div>
            <div className="relative min-w-[160px]"><button className="inline-flex h-10 w-full items-center justify-between rounded-md border border-strong bg-background px-3 text-sm">Public<ChevronDown size={14}/></button></div>
            <div className="ml-auto relative" ref={menuRef}>
              <button aria-label="More actions" onClick={() => setMenuOpen(!menuOpen)} className="grid h-9 w-9 place-items-center rounded-md border border-strong bg-background"><MoreVertical size={16}/></button>
              {menuOpen && (
                <div className="absolute right-0 z-10 mt-1 w-56 rounded-md border border-strong bg-popover shadow-lg">
                  <ul role="menu" className="p-1">
                    <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent"><Star size={16}/> Add to favorites</button></li>
                    <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent"><Send size={16}/> Publish</button></li>
                    <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent"><FilePenLine size={16}/> Unpublish</button></li>
                    <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent"><Copy size={16}/> Duplicate</button></li>
                    <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent"><Share2 size={16}/> Share</button></li>
                    <li><button className="flex h-9 w-full items-center gap-2 rounded px-2 text-sm hover:bg-accent text-destructive-foreground"><Trash2 size={16}/> Delete</button></li>
                  </ul>
                </div>
              )}
            </div>
          </div>
          <div className="mt-3">
            <label htmlFor="title" className="text-xs font-medium">Title</label>
            <input id="title" className="mt-1 h-10 w-full rounded-md border border-strong bg-background px-3 text-sm" defaultValue="Brainstorm product names" placeholder="Title" readOnly />
          </div>
          <div className="mt-2"><span className="badge badge-status-published">published</span></div>
          <label htmlFor="content" className="text-xs font-medium mt-3 block">Prompt</label>
          <textarea id="content" className="mt-1 min-h-[200px] w-full resize-y rounded-md border border-strong bg-background px-3 py-2 text-sm" defaultValue={"You are a creative branding assistant. Propose 20 product name ideas with a short rationale and a 1–10 novelty score. Output as a two-column markdown table."} readOnly />
          <div className="mt-3 rounded-xl border border-strong p-2">
            <div className="flex flex-wrap items-center gap-1.5">
              {['branding','marketing','ops'].map((t)=> (<span key={t} className="badge badge-tag">#{t}</span>))}
              <label htmlFor="tags" className="sr-only">Add tag</label>
              <input id="tags" className="min-w-[120px] flex-1 bg-transparent px-2 py-1 text-sm outline-none" placeholder="Add tag" readOnly />
            </div>
          </div>
          <div className="mt-3 flex flex-col gap-2"><button className="inline-flex h-10 w-full items-center justify-center gap-2 rounded-md bg-primary px-3 text-sm text-primary-foreground"><Copy size={16}/> Copy this prompt</button></div>
        </div>
      </ScrollArea>
    </section>
  )
}

/******************************
 * File: app/App.tsx (composition root)
 ******************************/
export default function App() {
  const [dark, setDark] = useState(true)
  const [menuOpen, setMenuOpen] = useState(false)
  const [explorerOpen, setExplorerOpen] = useState<string | null>(null)
  const [viewOpen, setViewOpen] = useState(false)
  const [modelOpen, setModelOpen] = useState(false)
  const [userOpen, setUserOpen] = useState(false)
  const [settingsOpen, setSettingsOpen] = useState(false)
  const [currentView, setCurrentView] = useState("All")
  const [currentModel, setCurrentModel] = useState("All models")
  const [selectedId, setSelectedId] = useState<number | null>(null)
  const [checkedIds, setCheckedIds] = useState<Set<number>>(new Set())
  const [cardMenuOpen, setCardMenuOpen] = useState<number | null>(null)

  const menuRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<HTMLDivElement>(null)
  const modelRef = useRef<HTMLDivElement>(null)
  const userRefDesktop = useRef<HTMLDivElement>(null)
  const settingsRefDesktop = useRef<HTMLDivElement>(null)

  // Apply dark class to <html>
  useEffect(() => {
    const root = document.documentElement
    if (dark) root.classList.add("dark")
    else root.classList.remove("dark")
  }, [dark])

  // Close menus on outside click or ESC
  useEffect(() => {
    const onDown = (e: any) => {
      if (menuRef.current && !menuRef.current.contains(e.target)) setMenuOpen(false)
      if (viewRef.current && !viewRef.current.contains(e.target)) setViewOpen(false)
      if (modelRef.current && !modelRef.current.contains(e.target)) setModelOpen(false)
      if (userRefDesktop.current && !userRefDesktop.current.contains(e.target)) setUserOpen(false)
      if (settingsRefDesktop.current && !settingsRefDesktop.current.contains(e.target)) setSettingsOpen(false)
      const inExplorer = e.target.closest('[data-explorer-menu-root]') || e.target.closest('[data-explorer-kebab]')
      if (!inExplorer) setExplorerOpen(null)
      const inCard = e.target.closest('[data-card-menu-root]') || e.target.closest('[data-card-kebab]')
      if (!inCard) setCardMenuOpen(null)
    }
    const onKey = (e: any) => { if (e.key === 'Escape') { setMenuOpen(false); setExplorerOpen(null); setViewOpen(false); setModelOpen(false); setUserOpen(false); setSettingsOpen(false); setCardMenuOpen(null) } }
    document.addEventListener('mousedown', onDown)
    document.addEventListener('keydown', onKey)
    return () => { document.removeEventListener('mousedown', onDown); document.removeEventListener('keydown', onKey) }
  }, [])

  // Runtime sanity tests
  useEffect(() => {
    const results = [] as { name: string; pass: boolean }[]
    results.push({ name: "has header", pass: !!document.querySelector("header") })
    results.push({ name: ">= 3 scroll areas", pass: document.querySelectorAll(".h-full.overflow-auto").length >= 3 })
    results.push({ name: "has title label", pass: !!document.querySelector('label[for="title"]') })
    results.push({ name: "has prompt label", pass: !!document.querySelector('label[for="content"]') })
    results.push({ name: "kebab exists", pass: !!document.querySelector('[aria-label="More actions"]') })
    results.push({ name: "user avatar exists", pass: !!document.querySelector('[aria-label="User menu"]') })
    results.push({ name: ">= 3 prompt cards", pass: document.querySelectorAll('.prompt-card').length >= 3 })
    results.push({ name: "odd rows styled", pass: document.querySelectorAll('.prompt-card.is-odd').length >= 2 })
    results.push({ name: "settings exists", pass: !!document.querySelector('[aria-label="Settings"]') })
    results.push({ name: "row menus exist", pass: document.querySelectorAll('[data-card-kebab]').length >= 3 })
    console.log("Self-tests:", results)
  }, [])

  const viewItems = ["All", "Favorites", "Drafts", "Published", "Archived"]
  const modelItems = ["All models", "GPT‑4o", "o3‑mini", "Llama 3.1‑70B", "Claude 3.5 Sonnet"]
  const simpleDate = "08/08/2025"

  const toggleChecked = (id: number) => {
    setCheckedIds(prev => {
      const next = new Set(prev)
      if (next.has(id)) next.delete(id); else next.add(id)
      return next
    })
  }

  return (
    <div className="h-screen bg-background text-foreground flex flex-col" style={{ fontFamily: "Inter, system-ui, sans-serif" }}>
      <ThemeStyles />

      <Header
        dark={dark}
        setDark={setDark}
        settingsOpen={settingsOpen}
        setSettingsOpen={setSettingsOpen}
        userOpen={userOpen}
        setUserOpen={setUserOpen}
        viewOpen={viewOpen}
        setViewOpen={setViewOpen}
        modelOpen={modelOpen}
        setModelOpen={setModelOpen}
        currentView={currentView}
        setCurrentView={setCurrentView}
        currentModel={currentModel}
        setCurrentModel={setCurrentModel}
        viewItems={viewItems}
        modelItems={modelItems}
        viewRef={viewRef}
        modelRef={modelRef}
        settingsRef={settingsRefDesktop}
        userRef={userRefDesktop}
      />

      {/* Body */}
      <div className="flex-1 overflow-hidden">
        <div className="hidden md:block h-full">
          <ResizablePanelGroup direction="horizontal" className="w-full h-full">
            <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
              <Explorer explorerOpen={explorerOpen} setExplorerOpen={setExplorerOpen} />
            </ResizablePanel>
            <ResizableHandle className="w-2 cursor-col-resize relative">
              <div className="absolute inset-y-0 left-1/2 -translate-x-1/2 w-px bg-[var(--border-strong)]" />
            </ResizableHandle>
            <ResizablePanel defaultSize={45} minSize={30}>
              <main className="h-full overflow-hidden">
                <ScrollArea className="h-full p-3 md:p-4">
                  <div className="grid grid-cols-1 gap-3">
                    {[1,2,3].map((i) => (
                      <PromptCard
                        key={i}
                        id={i}
                        index={i}
                        title={i === 1 ? "Brainstorm product names" : i === 2 ? "Summarize meeting notes" : "Generate test cases"}
                        preview={"You are a creative branding assistant. Propose 20 product name ideas with a short rationale and a novelty score…"}
                        tags={['branding','marketing','qa','testing'].slice(0, i+1)}
                        model={i === 1 ? 'GPT‑4o' : i === 2 ? 'Llama 3.1‑70B' : 'o3‑mini'}
                        isDraft={i === 3}
                        simpleDate={simpleDate}
                        selectedId={selectedId}
                        setSelectedId={setSelectedId}
                        checkedIds={checkedIds}
                        toggleChecked={toggleChecked}
                        cardMenuOpen={cardMenuOpen}
                        setCardMenuOpen={setCardMenuOpen}
                      />
                    ))}
                  </div>
                </ScrollArea>
              </main>
            </ResizablePanel>
            <ResizableHandle className="w-2 cursor-col-resize relative">
              <div className="absolute inset-y-0 left-1/2 -translate-x-1/2 w-px bg-[var(--border-strong)]" />
            </ResizableHandle>
            <ResizablePanel defaultSize={35} minSize={25}>
              <RightPanel menuRef={menuRef} menuOpen={menuOpen} setMenuOpen={setMenuOpen} />
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>

        {/* Mobile: middle pane only */}
        <div className="md:hidden h-full">
          <main className="h-full overflow-hidden">
            <ScrollArea className="h-full p-3">
              <div className="grid grid-cols-1 gap-3">
                {[1,2,3].map((i) => (
                  <PromptCard
                    key={i}
                    id={i}
                    index={i}
                    title={i === 1 ? "Brainstorm product names" : i === 2 ? "Summarize meeting notes" : "Generate test cases"}
                    preview={"You are a creative branding assistant. Propose 20 product name ideas with a short rationale and a novelty score…"}
                    tags={['branding','marketing','qa','testing'].slice(0, i+1)}
                    model={i === 1 ? 'GPT‑4o' : i === 2 ? 'Llama 3.1‑70B' : 'o3‑mini'}
                    isDraft={i === 3}
                    simpleDate={simpleDate}
                    selectedId={selectedId}
                    setSelectedId={setSelectedId}
                    checkedIds={checkedIds}
                    toggleChecked={toggleChecked}
                    cardMenuOpen={cardMenuOpen}
                    setCardMenuOpen={setCardMenuOpen}
                  />
                ))}
              </div>
            </ScrollArea>
          </main>
        </div>
      </div>

      {/* Mobile FAB */}
      <div className="md:hidden fixed bottom-4 right-4 z-20">
        <button className="grid h-12 w-12 place-items-center rounded-full bg-primary text-primary-foreground shadow-xl"><Plus/></button>
      </div>
    </div>
  )
}
