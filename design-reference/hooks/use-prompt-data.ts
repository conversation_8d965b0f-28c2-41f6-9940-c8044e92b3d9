"use client"
import { useState, useMemo } from "react"
import type { PromptFilters, PromptViewModel } from "@/types"
import { mockPrompts, currentUser } from "@/lib/mock-data"

export function usePromptData(): PromptViewModel & {
  updateFilters: (filters: Partial<PromptFilters>) => void
  clearFilters: () => void
} {
  const [filters, setFilters] = useState<PromptFilters>({})
  const [isLoading] = useState(false)

  const filteredPrompts = useMemo(() => {
    let filtered = mockPrompts

    // Filter by status
    if (filters.status) {
      filtered = filtered.filter((prompt) => prompt.status === filters.status)
    }

    // Filter by target AI
    if (filters.targetAI) {
      filtered = filtered.filter((prompt) => prompt.targetAI === filters.targetAI)
    }

    // Filter by author
    if (filters.authorId) {
      filtered = filtered.filter((prompt) => prompt.authorId === filters.authorId)
    }

    // Filter by tags
    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter((prompt) => filters.tags!.some((tagId) => prompt.tags.some((tag) => tag.id === tagId)))
    }

    // Filter by search text
    if (filters.searchText) {
      const searchLower = filters.searchText.toLowerCase()
      filtered = filtered.filter(
        (prompt) =>
          prompt.title.toLowerCase().includes(searchLower) || prompt.content.toLowerCase().includes(searchLower),
      )
    }

    // Filter by favorites
    if (filters.isFavorite) {
      filtered = filtered.filter((prompt) => prompt.isFavorite)
    }

    // Filter by shared with me
    if (filters.sharedWithMe) {
      filtered = filtered.filter(
        (prompt) => prompt.sharedWith.includes(currentUser.id) || prompt.authorId !== currentUser.id,
      )
    }

    return filtered
  }, [filters])

  const updateFilters = (newFilters: Partial<PromptFilters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }))
  }

  const clearFilters = () => {
    setFilters({})
  }

  return {
    prompts: mockPrompts,
    filteredPrompts,
    filters,
    isLoading,
    updateFilters,
    clearFilters,
  }
}
