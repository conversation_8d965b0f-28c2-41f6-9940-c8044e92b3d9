"use client"
import { useState } from "react"
import type { TargetAI } from "@/types"

export interface PromptDraft {
  title: string
  content: string
  targetAI: TargetAI
}

export function usePromptEditor(initialDraft?: PromptDraft) {
  const [draft, setDraft] = useState<PromptDraft>(
    initialDraft || {
      title: "",
      content: "",
      targetAI: "gpt-4",
    },
  )
  const [isLoading, setIsLoading] = useState(false)

  const createPromptDraft = async (draftData: PromptDraft) => {
    console.log("CreatePromptDraft", draftData)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  const editPromptDraft = async (promptId: string, draftData: PromptDraft) => {
    console.log("EditPromptDraft", promptId, draftData)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  const updateDraft = (updates: Partial<PromptDraft>) => {
    setDraft((prev) => ({ ...prev, ...updates }))
  }

  const resetDraft = () => {
    setDraft({
      title: "",
      content: "",
      targetAI: "gpt-4",
    })
  }

  return {
    draft,
    isLoading,
    updateDraft,
    resetDraft,
    createPromptDraft,
    editPromptDraft,
  }
}
