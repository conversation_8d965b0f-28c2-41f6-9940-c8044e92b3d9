"use client"
import { useState } from "react"

export function usePromptTags() {
  const [isLoading, setIsLoading] = useState(false)

  const addTagToPrompt = async (promptId: string, tagId: string) => {
    console.log("AddTagToPrompt", promptId, tagId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  const removeTagFromPrompt = async (promptId: string, tagId: string) => {
    console.log("RemoveTagFromPrompt", promptId, tagId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  return {
    isLoading,
    addTagToPrompt,
    removeTagFromPrompt,
  }
}
