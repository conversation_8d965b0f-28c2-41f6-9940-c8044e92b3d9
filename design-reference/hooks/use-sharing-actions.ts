"use client"
import { useState } from "react"
import type { PromptVisibility } from "@/types"

export function useSharingActions() {
  const [isLoading, setIsLoading] = useState(false)

  const removeUserAccessFromPrompt = async (promptId: string, userId: string) => {
    console.log("RemoveUserAccessFromPrompt", promptId, userId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  const setPromptVisibility = async (promptId: string, visibility: PromptVisibility) => {
    console.log("SetPromptVisibility", promptId, visibility)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  const sharePromptByLink = async (promptId: string) => {
    console.log("SharePromptByLink", promptId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
    // Return mock share link
    return `https://app.com/share/${promptId}-${Date.now()}`
  }

  const sharePromptWithUsers = async (promptId: string, userIds: string[]) => {
    console.log("SharePromptWithUsers", promptId, userIds)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  const viewPromptAccessList = async (promptId: string) => {
    console.log("ViewPromptAccessList", promptId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  return {
    isLoading,
    removeUserAccessFromPrompt,
    setPromptVisibility,
    sharePromptByLink,
    sharePromptWithUsers,
    viewPromptAccessList,
  }
}
