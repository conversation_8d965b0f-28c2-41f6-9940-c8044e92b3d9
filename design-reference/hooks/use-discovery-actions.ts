"use client"
import { useState } from "react"
import type { TargetAI, PromptStatus } from "@/types"

export function useDiscoveryActions() {
  const [isLoading, setIsLoading] = useState(false)

  const filterPromptsByAuthor = async (authorId: string) => {
    console.log("FilterPromptsByAuthor", authorId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  const filterPromptsByAuthorOrShared = async (authorId: string) => {
    console.log("FilterPromptsByAuthorOrShared", authorId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  const filterPromptsByTargetAI = async (targetAI: TargetAI) => {
    console.log("FilterPromptsByTargetAI", targetAI)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  const filterPromptsByTag = async (tagId: string) => {
    console.log("FilterPromptsByTag", tagId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  const listFavoritePrompts = async () => {
    console.log("ListFavoritePrompts")
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  const listPromptsByStatus = async (status: PromptStatus) => {
    console.log("ListPromptsByStatus", status)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  const searchPromptsByText = async (searchText: string) => {
    console.log("SearchPromptsByText", searchText)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  return {
    isLoading,
    filterPromptsByAuthor,
    filterPromptsByAuthorOrShared,
    filterPromptsByTargetAI,
    filterPromptsByTag,
    listFavoritePrompts,
    listPromptsByStatus,
    searchPromptsByText,
  }
}
