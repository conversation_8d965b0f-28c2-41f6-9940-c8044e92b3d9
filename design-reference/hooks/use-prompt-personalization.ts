"use client"

import { useState, useMemo } from "react"

export function usePromptPersonalization(content: string) {
  const [tokenValues, setTokenValues] = useState<Record<string, string>>({})

  // Extract tokens from prompt content (e.g., [EMAIL_CONTENT], [TONE])
  const tokens = useMemo(() => {
    const tokenRegex = /\[([A-Z_]+)\]/g
    const matches = content.match(tokenRegex) || []
    return [...new Set(matches)].map((token) => ({
      token,
      key: token.slice(1, -1), // Remove brackets
      placeholder: token.slice(1, -1).toLowerCase().replace(/_/g, " "),
    }))
  }, [content])

  // Generate personalized content by replacing tokens with values
  const personalizedContent = useMemo(() => {
    let result = content
    tokens.forEach(({ token, key }) => {
      const value = tokenValues[key] || token
      result = result.replace(new RegExp(`\\${token}`, "g"), value)
    })
    return result
  }, [content, tokens, tokenValues])

  const updateTokenValue = (key: string, value: string) => {
    setTokenValues((prev) => ({ ...prev, [key]: value }))
  }

  const resetTokens = () => {
    setTokenValues({})
  }

  const hasTokens = tokens.length > 0
  const hasPersonalizedValues = Object.keys(tokenValues).some((key) => tokenValues[key])

  const createPromptFromPersonalized = () => {
    console.log("CreatePromptFromPersonalized", { personalizedContent })
  }

  return {
    tokens,
    tokenValues,
    personalizedContent,
    updateTokenValue,
    resetTokens,
    hasTokens,
    hasPersonalizedValues,
    createPromptFromPersonalized,
  }
}
