"use client"
import { useState, useMemo } from "react"
import type { CollaborationViewModel } from "@/types"
import { mockComments, mockNotifications, currentUser } from "@/lib/mock-data"

export function useCollaborationData(): CollaborationViewModel & {
  markNotificationAsRead: (notificationId: string) => void
  markAllNotificationsAsRead: () => void
} {
  const [notifications, setNotifications] = useState(mockNotifications)
  const [isLoading] = useState(false)

  const unreadCount = useMemo(() => {
    return notifications.filter((n) => !n.isRead && n.userId === currentUser.id).length
  }, [notifications])

  const markNotificationAsRead = (notificationId: string) => {
    console.log("MarkNotificationAsRead", notificationId)
    setNotifications((prev) => prev.map((n) => (n.id === notificationId ? { ...n, isRead: true } : n)))
  }

  const markAllNotificationsAsRead = () => {
    console.log("MarkAllNotificationsAsRead")
    setNotifications((prev) => prev.map((n) => (n.userId === currentUser.id ? { ...n, isRead: true } : n)))
  }

  return {
    comments: mockComments,
    notifications: notifications.filter((n) => n.userId === currentUser.id),
    unreadCount,
    isLoading,
    markNotificationAsRead,
    markAllNotificationsAsRead,
  }
}
