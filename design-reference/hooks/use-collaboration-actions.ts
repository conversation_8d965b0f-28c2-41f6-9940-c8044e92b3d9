"use client"
import { useState } from "react"

export function useCollaborationActions() {
  const [isLoading, setIsLoading] = useState(false)

  const addCommentToPrompt = async (promptId: string, content: string) => {
    console.log("AddCommentToPrompt", promptId, content)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  const deleteComment = async (commentId: string) => {
    console.log("DeleteComment", commentId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  const editComment = async (commentId: string, content: string) => {
    console.log("EditComment", commentId, content)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 500))
    setIsLoading(false)
  }

  const notifyNewCommentOnFollowedPrompt = async (promptId: string, commentId: string) => {
    console.log("NotifyNewCommentOnFollowedPrompt", promptId, commentId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  const notifyPromptSharedWithMe = async (promptId: string, sharedByUserId: string) => {
    console.log("NotifyPromptSharedWithMe", promptId, sharedByUserId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  const receivePromptCommentsInRealtime = async (promptId: string) => {
    console.log("ReceivePromptCommentsInRealtime", promptId)
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 300))
    setIsLoading(false)
  }

  return {
    isLoading,
    addCommentToPrompt,
    deleteComment,
    editComment,
    notifyNewCommentOnFollowedPrompt,
    notifyPromptSharedWithMe,
    receivePromptCommentsInRealtime,
  }
}
