@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Updated to use the provided theme configuration with purple-based color scheme */
:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.2395 0.0059 271.1652);
  --card: oklch(0.9761 0 0);
  --card-foreground: oklch(0.2395 0.0059 271.1652);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.2395 0.0059 271.1652);
  --primary: oklch(0.5311 0.1937 258.5750);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9249 0 0);
  --secondary-foreground: oklch(0.2395 0.0059 271.1652);
  --muted: oklch(0.9551 0 0);
  --muted-foreground: oklch(0.5575 0.0165 244.8933);
  --accent: oklch(0.9249 0 0);
  --accent-foreground: oklch(0.2395 0.0059 271.1652);
  --destructive: oklch(0.6083 0.2090 27.0276);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8945 0 0);
  --input: oklch(0.9249 0 0);
  --ring: oklch(0.5311 0.1937 258.5750);
  --chart-1: oklch(0.5311 0.1937 258.5750);
  --chart-2: oklch(0.6958 0.1596 55.5444);
  --chart-3: oklch(0.6629 0.1602 152.3714);
  --chart-4: oklch(0.6062 0.2298 9.6281);
  --chart-5: oklch(0.5261 0.1705 314.6534);
  --sidebar: oklch(0.9761 0 0);
  --sidebar-foreground: oklch(0.5575 0.0165 244.8933);
  --sidebar-primary: oklch(0.5311 0.1937 258.5750);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9249 0 0);
  --sidebar-accent-foreground: oklch(0.2395 0.0059 271.1652);
  --sidebar-border: oklch(0.8945 0 0);
  --sidebar-ring: oklch(0.5311 0.1937 258.5750);
  --font-sans: Inter;
  --font-serif: Georgia;
  --font-mono: JetBrains Mono;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.03);
  --shadow-xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.03);
  --shadow-sm: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);
  --shadow: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);
  --shadow-md: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 2px 4px -1px hsl(0 0% 0% / 0.05);
  --shadow-lg: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 4px 6px -1px hsl(0 0% 0% / 0.05);
  --shadow-xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 8px 10px -1px hsl(0 0% 0% / 0.05);
  --shadow-2xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.13);
  --tracking-normal: 0rem;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2395 0.0059 271.1652);
  --foreground: oklch(0.9551 0 0);
  --card: oklch(0.2963 0.0061 258.3551);
  --card-foreground: oklch(0.9551 0 0);
  --popover: oklch(0.2963 0.0061 258.3551);
  --popover-foreground: oklch(0.9551 0 0);
  --primary: oklch(0.6755 0.1765 252.2592);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.4212 0.0170 272.3799);
  --secondary-foreground: oklch(0.9551 0 0);
  --muted: oklch(0.3410 0.0146 269.2577);
  --muted-foreground: oklch(0.7733 0.0265 250.0164);
  --accent: oklch(0.3410 0.0146 269.2577);
  --accent-foreground: oklch(0.9551 0 0);
  --destructive: oklch(0.6004 0.1661 23.5439);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3410 0.0146 269.2577);
  --input: oklch(0.3410 0.0146 269.2577);
  --ring: oklch(0.6755 0.1765 252.2592);
  --chart-1: oklch(0.6755 0.1765 252.2592);
  --chart-2: oklch(0.7720 0.1738 64.5520);
  --chart-3: oklch(0.7395 0.2268 142.8504);
  --chart-4: oklch(0.6702 0.2462 356.4011);
  --chart-5: oklch(0.5817 0.2729 300.2277);
  --sidebar: oklch(0.2963 0.0061 258.3551);
  --sidebar-foreground: oklch(0.7733 0.0265 250.0164);
  --sidebar-primary: oklch(0.6755 0.1765 252.2592);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.3410 0.0146 269.2577);
  --sidebar-accent-foreground: oklch(0.9551 0 0);
  --sidebar-border: oklch(0.3410 0.0146 269.2577);
  --sidebar-ring: oklch(0.6755 0.1765 252.2592);
  --font-sans: Inter;
  --font-serif: Georgia;
  --font-mono: JetBrains Mono;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}
