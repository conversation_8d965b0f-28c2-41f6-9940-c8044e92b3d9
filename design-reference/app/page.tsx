"use client"
import { useState, useEffect } from "react"
import { UserAvatar } from "@/components/user-avatar"
import { NotificationsDropdown } from "@/components/notifications-dropdown"
import { PromptList } from "@/components/prompt-list"
import { PromptEditor } from "@/components/prompt-editor"
import { PromptDetailView } from "@/components/prompt-detail-view"
import { FloatingActionButton } from "@/components/floating-action-button"
import { SearchBar } from "@/components/search-bar"
import { FilterChips } from "@/components/filter-chips"
import { FilterMenu } from "@/components/filter-menu"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { usePromptData } from "@/hooks/use-prompt-data"
import { useCollaborationData } from "@/hooks/use-collaboration-data"
import { useDiscoveryActions } from "@/hooks/use-discovery-actions"
import type { Prompt, PromptFilters } from "@/types"

type ViewMode = "list" | "detail" | "editor"

export default function HomePage() {
  const { filteredPrompts, filters, updateFilters, clearFilters } = usePromptData()
  const { comments } = useCollaborationData()
  const { searchPromptsByText } = useDiscoveryActions()
  const [viewMode, setViewMode] = useState<ViewMode>("list")
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | undefined>()
  const [editingPrompt, setEditingPrompt] = useState<Prompt | undefined>()
  const [searchValue, setSearchValue] = useState("")

  // Sync search value with filters
  useEffect(() => {
    setSearchValue(filters.searchText || "")
  }, [filters.searchText])

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue !== (filters.searchText || "")) {
        updateFilters({ searchText: searchValue || undefined })
        if (searchValue) {
          searchPromptsByText(searchValue)
        }
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [searchValue, filters.searchText, updateFilters, searchPromptsByText])

  const handleCreatePrompt = () => {
    setEditingPrompt(undefined)
    setViewMode("editor")
  }

  const handleEditPrompt = (prompt: Prompt) => {
    setEditingPrompt(prompt)
    setViewMode("editor")
  }

  const handleViewPrompt = (prompt: Prompt) => {
    setSelectedPrompt(prompt)
    setViewMode("detail")
  }

  const handleSavePrompt = () => {
    setViewMode("list")
    setEditingPrompt(undefined)
  }

  const handleCancelEdit = () => {
    setViewMode("list")
    setEditingPrompt(undefined)
  }

  const handleBackToList = () => {
    setViewMode("list")
    setSelectedPrompt(undefined)
  }

  const handleRemoveFilter = (filterType: keyof PromptFilters, value?: string) => {
    if (filterType === "tags" && value) {
      const currentTags = filters.tags || []
      const newTags = currentTags.filter((id) => id !== value)
      updateFilters({ tags: newTags.length > 0 ? newTags : undefined })
    } else {
      updateFilters({ [filterType]: undefined })
    }
  }

  const handleClearSearch = () => {
    setSearchValue("")
    updateFilters({ searchText: undefined })
  }

  const handleCommentsUpdated = () => {
    // In a real app, this would refresh the comments data
    console.log("Comments updated, refreshing data...")
  }

  const handlePromptUpdated = () => {
    // In a real app, this would refresh the prompt data
    console.log("Prompt updated, refreshing data...")
  }

  if (viewMode === "editor") {
    return (
      <div className="min-h-screen bg-background">
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex h-14 items-center justify-between px-4 sm:px-6 md:px-8 lg:px-12 max-w-4xl lg:max-w-7xl mx-auto">
            <div className="flex items-center space-x-2">
              <h1 className="text-lg font-semibold sm:text-xl lg:text-2xl">Prompt Manager</h1>
            </div>
            <div className="flex items-center gap-2 lg:gap-4">
              <Button onClick={handleCreatePrompt} className="hidden lg:flex lg:items-center lg:gap-2">
                <Plus className="h-4 w-4" />
                Create Prompt
              </Button>
              <NotificationsDropdown />
              <UserAvatar />
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 py-6 lg:py-8 max-w-md sm:max-w-2xl md:max-w-4xl lg:max-w-7xl pb-24">
          <PromptEditor
            prompt={editingPrompt}
            onSave={handleSavePrompt}
            onCancel={handleCancelEdit}
            onBack={handleBackToList}
          />
        </main>
      </div>
    )
  }

  if (viewMode === "detail" && selectedPrompt) {
    return (
      <div className="min-h-screen bg-background">
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex h-14 items-center justify-between px-4 sm:px-6 md:px-8 lg:px-12 max-w-4xl lg:max-w-7xl mx-auto">
            <div className="flex items-center space-x-2">
              <h1 className="text-lg font-semibold sm:text-xl lg:text-2xl">Prompt Manager</h1>
            </div>
            <div className="flex items-center gap-2 lg:gap-4">
              <Button onClick={handleCreatePrompt} className="hidden lg:flex lg:items-center lg:gap-2">
                <Plus className="h-4 w-4" />
                Create Prompt
              </Button>
              <NotificationsDropdown />
              <UserAvatar />
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 py-6 lg:py-8 max-w-md sm:max-w-2xl md:max-w-4xl lg:max-w-7xl pb-24">
          <PromptDetailView
            prompt={selectedPrompt}
            comments={comments}
            onBack={handleBackToList}
            onEdit={handleEditPrompt}
            onCommentsUpdated={handleCommentsUpdated}
            onPromptUpdated={handlePromptUpdated}
          />
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-14 items-center justify-between px-4 sm:px-6 md:px-8 lg:px-12 max-w-6xl lg:max-w-7xl mx-auto">
          <div className="flex items-center space-x-2">
            <h1 className="text-lg font-semibold sm:text-xl lg:text-2xl">Prompt Manager</h1>
          </div>
          <div className="flex items-center gap-2 lg:gap-4">
            <Button onClick={handleCreatePrompt} className="hidden lg:flex lg:items-center lg:gap-2">
              <Plus className="h-4 w-4" />
              Create Prompt
            </Button>
            <NotificationsDropdown />
            <UserAvatar />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 py-6 lg:py-8 max-w-md sm:max-w-2xl md:max-w-6xl lg:max-w-7xl pb-24">
        <div className="space-y-6 lg:space-y-8">
          <div className="space-y-4 lg:space-y-6">
            <div className="flex gap-2 sm:gap-3 lg:gap-4">
              <div className="flex-1">
                <SearchBar
                  value={searchValue}
                  onChange={setSearchValue}
                  onClear={handleClearSearch}
                  placeholder="Search prompts..."
                />
              </div>
              <FilterMenu filters={filters} onFilterChange={updateFilters} />
            </div>

            <FilterChips filters={filters} onRemoveFilter={handleRemoveFilter} onClearAll={clearFilters} />
          </div>

          <div className="w-full">
            <PromptList
              prompts={filteredPrompts}
              onEditPrompt={handleEditPrompt}
              onViewPrompt={handleViewPrompt}
              onPromptUpdated={handlePromptUpdated}
              emptyMessage="No prompts found. Try adjusting your filters or create your first prompt!"
            />
          </div>
        </div>
      </main>

      <FloatingActionButton onClick={handleCreatePrompt} />
    </div>
  )
}
