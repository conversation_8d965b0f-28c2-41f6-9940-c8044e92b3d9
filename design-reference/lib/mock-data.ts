import type { User, Tag, Prompt, Comment, Notification } from "@/types"

// Mock users
export const mockUsers: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=32&width=32",
    createdAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=32&width=32",
    createdAt: new Date("2024-01-02"),
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=32&width=32",
    createdAt: new Date("2024-01-03"),
  },
]

// Mock tags
export const mockTags: Tag[] = [
  { id: "1", name: "Writing", color: "#3b82f6", createdAt: new Date("2024-01-01") },
  { id: "2", name: "<PERSON>", color: "#10b981", createdAt: new Date("2024-01-01") },
  { id: "3", name: "Analysis", color: "#f59e0b", createdAt: new Date("2024-01-01") },
  { id: "4", name: "Creative", color: "#ef4444", createdAt: new Date("2024-01-01") },
  { id: "5", name: "Business", color: "#8b5cf6", createdAt: new Date("2024-01-01") },
]

// Mock prompts
export const mockPrompts: Prompt[] = [
  {
    id: "1",
    title: "Blog Post Writer",
    content:
      "Write a comprehensive blog post about [TOPIC]. Include an engaging introduction, 3-5 main points with examples, and a compelling conclusion. Target audience: [AUDIENCE]. Tone: [TONE].",
    status: "published",
    visibility: "public",
    targetAI: "gpt-4",
    authorId: "1",
    author: mockUsers[0],
    tags: [mockTags[0], mockTags[3]],
    isFavorite: true,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-16"),
    sharedWith: ["2"],
    commentCount: 3,
  },
  {
    id: "2",
    title: "Code Review Assistant",
    content:
      "Review the following code for best practices, potential bugs, and performance improvements. Provide specific suggestions with explanations:\n\n[CODE]",
    status: "published",
    visibility: "private",
    targetAI: "claude",
    authorId: "1",
    author: mockUsers[0],
    tags: [mockTags[1]],
    isFavorite: false,
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-12"),
    sharedWith: [],
    commentCount: 1,
  },
  {
    id: "3",
    title: "Market Analysis Template",
    content:
      "Analyze the market for [PRODUCT/SERVICE]. Include: 1) Market size and growth trends, 2) Key competitors and their positioning, 3) Target customer segments, 4) Opportunities and threats, 5) Recommendations for market entry.",
    status: "draft",
    visibility: "private",
    targetAI: "gemini",
    authorId: "1",
    author: mockUsers[0],
    tags: [mockTags[2], mockTags[4]],
    isFavorite: false,
    createdAt: new Date("2024-01-20"),
    updatedAt: new Date("2024-01-21"),
    sharedWith: [],
    commentCount: 0,
  },
  {
    id: "4",
    title: "Creative Story Starter",
    content:
      "Create an engaging opening for a short story with the following elements: Setting: [SETTING], Main character: [CHARACTER], Conflict: [CONFLICT]. Write in [GENRE] style with a [TONE] tone.",
    status: "published",
    visibility: "shared",
    targetAI: "gpt-4",
    authorId: "2",
    author: mockUsers[1],
    tags: [mockTags[0], mockTags[3]],
    isFavorite: true,
    createdAt: new Date("2024-01-18"),
    updatedAt: new Date("2024-01-19"),
    sharedWith: ["1", "3"],
    shareLink: "https://app.com/share/abc123",
    commentCount: 2,
  },
  {
    id: "5",
    title: "Email Response Generator",
    content:
      "Generate a professional email response to: [EMAIL_CONTENT]. Tone should be [TONE]. Include: acknowledgment of their message, clear response to their questions/requests, and appropriate next steps.",
    status: "archived",
    visibility: "private",
    targetAI: "claude",
    authorId: "1",
    author: mockUsers[0],
    tags: [mockTags[0], mockTags[4]],
    isFavorite: false,
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-08"),
    sharedWith: [],
    commentCount: 0,
  },
]

// Mock comments
export const mockComments: Comment[] = [
  {
    id: "1",
    promptId: "1",
    authorId: "2",
    author: mockUsers[1],
    content: "This is a great template! I've been using it for my blog posts and it works perfectly.",
    createdAt: new Date("2024-01-17"),
  },
  {
    id: "2",
    promptId: "1",
    authorId: "3",
    author: mockUsers[2],
    content: "Could you add a section about SEO optimization?",
    createdAt: new Date("2024-01-18"),
  },
  {
    id: "3",
    promptId: "1",
    authorId: "1",
    author: mockUsers[0],
    content: "Good suggestion! I'll update it to include SEO guidelines.",
    createdAt: new Date("2024-01-18"),
  },
  {
    id: "4",
    promptId: "2",
    authorId: "2",
    author: mockUsers[1],
    content: "Very thorough code review prompt. Helped me catch several issues.",
    createdAt: new Date("2024-01-13"),
  },
  {
    id: "5",
    promptId: "4",
    authorId: "1",
    author: mockUsers[0],
    content: "Love the creative approach! This sparked some great story ideas.",
    createdAt: new Date("2024-01-19"),
  },
  {
    id: "6",
    promptId: "4",
    authorId: "3",
    author: mockUsers[2],
    content: "Perfect for overcoming writer's block.",
    createdAt: new Date("2024-01-20"),
  },
]

// Mock notifications
export const mockNotifications: Notification[] = [
  {
    id: "1",
    userId: "1",
    type: "comment",
    title: "New comment on your prompt",
    message: 'Jane Smith commented on "Blog Post Writer"',
    isRead: false,
    relatedPromptId: "1",
    relatedCommentId: "1",
    createdAt: new Date("2024-01-17"),
  },
  {
    id: "2",
    userId: "1",
    type: "share",
    title: "Prompt shared with you",
    message: 'Jane Smith shared "Creative Story Starter" with you',
    isRead: false,
    relatedPromptId: "4",
    createdAt: new Date("2024-01-18"),
  },
  {
    id: "3",
    userId: "1",
    type: "comment",
    title: "New comment on your prompt",
    message: 'Mike Johnson commented on "Blog Post Writer"',
    isRead: true,
    relatedPromptId: "1",
    relatedCommentId: "2",
    createdAt: new Date("2024-01-18"),
  },
]

// Current user (for demo purposes)
export const currentUser = mockUsers[0]
