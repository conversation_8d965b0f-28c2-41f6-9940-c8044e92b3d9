import type { TargetAI } from "@/types"

export const TARGET_AI_OPTIONS: { value: TargetAI; label: string; description: string }[] = [
  { value: "gpt-4", label: "GPT-4", description: "OpenAI GPT-4" },
  { value: "claude", label: "<PERSON>", description: "Anthropic Claude" },
  { value: "gemini", label: "Gemini", description: "Google Gemini" },
  { value: "llama", label: "Llama", description: "Meta Llama" },
  { value: "custom", label: "Custom", description: "Custom AI model" },
]

export const PROMPT_STATUS_LABELS = {
  draft: "Draft",
  published: "Published",
  archived: "Archived",
} as const

export const VISIBILITY_LABELS = {
  private: "Private",
  public: "Public",
  shared: "Shared",
} as const

export const NOTIFICATION_TYPES = {
  comment: "Comment",
  share: "Share",
  mention: "Mention",
} as const
