"use client"
import { Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface FloatingActionButtonProps {
  onClick: () => void
}

export function FloatingActionButton({ onClick }: FloatingActionButtonProps) {
  return (
    <Button
      size="icon"
      className="fixed bottom-6 right-4 h-12 w-12 rounded-full shadow-lg z-50 sm:bottom-8 sm:right-6 sm:h-14 sm:w-14 md:bottom-10 md:right-8 lg:hidden"
      onClick={onClick}
    >
      <Plus className="h-5 w-5 sm:h-6 sm:w-6" />
    </Button>
  )
}
