"use client"
import { useState } from "react"
import { MoreVertical, Edit, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import type { Comment } from "@/types"
import { currentUser } from "@/lib/mock-data"
import { useCollaborationActions } from "@/hooks/use-collaboration-actions"

interface CommentItemProps {
  comment: Comment
  onCommentUpdated?: () => void
}

export function CommentItem({ comment, onCommentUpdated }: CommentItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(comment.content)
  const { editComment, deleteComment, isLoading } = useCollaborationActions()

  const isOwner = comment.authorId === currentUser.id

  const handleEdit = () => {
    setIsEditing(true)
    setEditContent(comment.content)
  }

  const handleSaveEdit = async () => {
    if (editContent.trim() && editContent !== comment.content) {
      await editComment(comment.id, editContent.trim())
      onCommentUpdated?.()
    }
    setIsEditing(false)
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditContent(comment.content)
  }

  const handleDelete = async () => {
    await deleteComment(comment.id)
    onCommentUpdated?.()
  }

  return (
    <div className="space-y-2">
      <div className="flex items-start justify-between gap-2">
        <div className="flex items-center gap-2">
          <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-bold">
            {comment.author.name.charAt(0)}
          </div>
          <div>
            <span className="text-sm font-medium">{comment.author.name}</span>
            <span className="text-xs text-muted-foreground ml-2">
              {new Date(comment.createdAt).toLocaleDateString()}
            </span>
          </div>
        </div>
        {isOwner && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <MoreVertical className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDelete} className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-2">
          <Textarea
            value={editContent}
            onChange={(e) => setEditContent(e.target.value)}
            rows={3}
            className="resize-none"
          />
          <div className="flex gap-2 justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancelEdit}
              disabled={isLoading}
              className="bg-transparent"
            >
              Cancel
            </Button>
            <Button size="sm" onClick={handleSaveEdit} disabled={isLoading || !editContent.trim()}>
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      ) : (
        <p className="text-sm text-muted-foreground pl-8">{comment.content}</p>
      )}
    </div>
  )
}
