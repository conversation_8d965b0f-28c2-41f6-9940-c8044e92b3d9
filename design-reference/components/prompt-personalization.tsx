"use client"
import { useState } from "react"
import { <PERSON><PERSON>, Check, Plus, RotateCcw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { usePromptPersonalization } from "@/hooks/use-prompt-personalization"
import { useClipboard } from "@/hooks/use-clipboard"

interface PromptPersonalizationProps {
  content: string
}

export function PromptPersonalization({ content }: PromptPersonalizationProps) {
  const {
    tokens,
    tokenValues,
    personalizedContent,
    updateTokenValue,
    resetTokens,
    hasTokens,
    hasPersonalizedValues,
    createPromptFromPersonalized,
  } = usePromptPersonalization(content)

  const { copyToClipboard, isCopied } = useClipboard()
  const [isExpanded, setIsExpanded] = useState(false)

  if (!hasTokens) {
    return null
  }

  const handleCopyPersonalized = () => {
    copyToClipboard(personalizedContent)
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Personalize Template</CardTitle>
          <Button variant="ghost" size="sm" onClick={() => setIsExpanded(!isExpanded)} className="text-xs">
            {isExpanded ? "Collapse" : "Customize"}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          This prompt contains {tokens.length} customizable token{tokens.length > 1 ? "s" : ""}
        </p>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
          {/* Token Inputs */}
          <div className="space-y-3">
            {tokens.map(({ token, key, placeholder }) => (
              <div key={key} className="space-y-1">
                <Label htmlFor={key} className="text-xs font-medium">
                  {token}
                </Label>
                <Input
                  id={key}
                  placeholder={`Enter ${placeholder}...`}
                  value={tokenValues[key] || ""}
                  onChange={(e) => updateTokenValue(key, e.target.value)}
                  className="text-sm"
                />
              </div>
            ))}
          </div>

          {/* Preview */}
          {hasPersonalizedValues && (
            <div className="space-y-2">
              <Label className="text-xs font-medium">Preview</Label>
              <Textarea value={personalizedContent} readOnly className="min-h-[100px] text-sm bg-muted/50" />
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-2 pt-2">
            <Button
              variant="default"
              size="sm"
              onClick={handleCopyPersonalized}
              className="flex-1 h-8 text-xs"
              disabled={!hasPersonalizedValues}
            >
              {isCopied ? <Check className="h-3.5 w-3.5 mr-1.5" /> : <Copy className="h-3.5 w-3.5 mr-1.5" />}
              {isCopied ? "Copied!" : "Copy Result"}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={createPromptFromPersonalized}
              className="flex-1 h-8 text-xs bg-transparent"
              disabled={!hasPersonalizedValues}
            >
              <Plus className="h-3.5 w-3.5 mr-1.5" />
              Create Prompt
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={resetTokens}
              className="h-8 px-2"
              disabled={!hasPersonalizedValues}
            >
              <RotateCcw className="h-3.5 w-3.5" />
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
