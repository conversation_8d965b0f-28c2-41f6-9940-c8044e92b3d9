"use client"
import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>, Refresh<PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useSharingActions } from "@/hooks/use-sharing-actions"

interface ShareLinkSectionProps {
  promptId: string
  currentShareLink?: string
  disabled?: boolean
}

export function ShareLinkSection({ promptId, currentShareLink, disabled }: ShareLinkSectionProps) {
  const [shareLink, setShareLink] = useState(currentShareLink || "")
  const [copied, setCopied] = useState(false)
  const { sharePromptByLink, isLoading } = useSharingActions()

  const handleGenerateLink = async () => {
    const newLink = await sharePromptByLink(promptId)
    setShareLink(newLink)
  }

  const handleCopyLink = async () => {
    if (shareLink) {
      await navigator.clipboard.writeText(shareLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium flex items-center gap-2">
        <Link className="h-4 w-4" />
        Share link
      </Label>

      <div className="space-y-2">
        {shareLink ? (
          <div className="flex gap-2">
            <Input value={shareLink} readOnly className="text-xs" />
            <Button variant="outline" size="icon" onClick={handleCopyLink} disabled={disabled}>
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <p className="text-sm text-muted-foreground">No share link generated yet</p>
        )}

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleGenerateLink}
            disabled={disabled || isLoading}
            className="bg-transparent"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            {shareLink ? "Regenerate Link" : "Generate Link"}
          </Button>
          {copied && <span className="text-sm text-green-600 self-center">Copied!</span>}
        </div>
      </div>
    </div>
  )
}
