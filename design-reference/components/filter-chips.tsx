"use client"
import { X, ChevronDown } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import type { PromptFilters } from "@/types"
import { TARGET_AI_OPTIONS, PROMPT_STATUS_LABELS } from "@/lib/constants"
import { mockTags, mockUsers } from "@/lib/mock-data"

interface FilterChipsProps {
  filters: PromptFilters
  onRemoveFilter: (filterType: keyof PromptFilters, value?: string) => void
  onClearAll: () => void
}

export function FilterChips({ filters, onRemoveFilter, onClearAll }: FilterChipsProps) {
  const activeFilters = []

  // Search text filter
  if (filters.searchText) {
    activeFilters.push({
      type: "searchText" as const,
      label: `Search: "${filters.searchText}"`,
      value: filters.searchText,
    })
  }

  // Status filter
  if (filters.status) {
    activeFilters.push({
      type: "status" as const,
      label: `Status: ${PROMPT_STATUS_LABELS[filters.status]}`,
      value: filters.status,
    })
  }

  // Target AI filter
  if (filters.targetAI) {
    const aiLabel = TARGET_AI_OPTIONS.find((ai) => ai.value === filters.targetAI)?.label || filters.targetAI
    activeFilters.push({
      type: "targetAI" as const,
      label: `AI: ${aiLabel}`,
      value: filters.targetAI,
    })
  }

  // Author filter
  if (filters.authorId) {
    const author = mockUsers.find((user) => user.id === filters.authorId)
    activeFilters.push({
      type: "authorId" as const,
      label: `Author: ${author?.name || "Unknown"}`,
      value: filters.authorId,
    })
  }

  // Tags filter
  if (filters.tags && filters.tags.length > 0) {
    filters.tags.forEach((tagId) => {
      const tag = mockTags.find((t) => t.id === tagId)
      if (tag) {
        activeFilters.push({
          type: "tags" as const,
          label: `Tag: ${tag.name}`,
          value: tagId,
        })
      }
    })
  }

  // Favorites filter
  if (filters.isFavorite) {
    activeFilters.push({
      type: "isFavorite" as const,
      label: "Favorites only",
      value: "true",
    })
  }

  // Shared with me filter
  if (filters.sharedWithMe) {
    activeFilters.push({
      type: "sharedWithMe" as const,
      label: "Shared with me",
      value: "true",
    })
  }

  if (activeFilters.length === 0) {
    return null
  }

  const groupedFilters = {
    search: activeFilters.filter((f) => f.type === "searchText"),
    content: activeFilters.filter((f) => ["status", "targetAI", "isFavorite", "sharedWithMe"].includes(f.type)),
    people: activeFilters.filter((f) => f.type === "authorId"),
    tags: activeFilters.filter((f) => f.type === "tags"),
  }

  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-2">
        {/* Search filters */}
        {groupedFilters.search.map((filter, index) => (
          <Badge
            key={`${filter.type}-${filter.value}-${index}`}
            variant="secondary"
            className="flex items-center gap-1"
          >
            <span className="text-xs">{filter.label}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-3 w-3 p-0 hover:bg-transparent"
              onClick={() => onRemoveFilter(filter.type)}
            >
              <X className="h-2 w-2" />
            </Button>
          </Badge>
        ))}

        {/* Content filters */}
        {groupedFilters.content.map((filter, index) => (
          <Badge
            key={`${filter.type}-${filter.value}-${index}`}
            variant="secondary"
            className="flex items-center gap-1"
          >
            <span className="text-xs">{filter.label}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-3 w-3 p-0 hover:bg-transparent"
              onClick={() => onRemoveFilter(filter.type)}
            >
              <X className="h-2 w-2" />
            </Button>
          </Badge>
        ))}

        {/* People filters - collapsed when many authors */}
        {groupedFilters.people.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Badge variant="secondary" className="flex items-center gap-1 cursor-pointer">
                <span className="text-xs">
                  {groupedFilters.people.length === 1
                    ? groupedFilters.people[0].label
                    : `${groupedFilters.people.length} Authors`}
                </span>
                <ChevronDown className="h-2 w-2" />
              </Badge>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {groupedFilters.people.map((filter, index) => (
                <DropdownMenuItem
                  key={`${filter.type}-${filter.value}-${index}`}
                  onClick={() => onRemoveFilter(filter.type)}
                  className="flex items-center justify-between"
                >
                  <span className="text-xs">{filter.label}</span>
                  <X className="h-3 w-3" />
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Tag filters - collapsed when many tags */}
        {groupedFilters.tags.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Badge variant="secondary" className="flex items-center gap-1 cursor-pointer">
                <span className="text-xs">
                  {groupedFilters.tags.length === 1
                    ? groupedFilters.tags[0].label
                    : `${groupedFilters.tags.length} Tags`}
                </span>
                <ChevronDown className="h-2 w-2" />
              </Badge>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {groupedFilters.tags.map((filter, index) => (
                <DropdownMenuItem
                  key={`${filter.type}-${filter.value}-${index}`}
                  onClick={() => onRemoveFilter(filter.type, filter.value)}
                  className="flex items-center justify-between"
                >
                  <span className="text-xs">{filter.label}</span>
                  <X className="h-3 w-3" />
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
      {activeFilters.length > 1 && (
        <Button variant="outline" size="sm" onClick={onClearAll} className="h-7 text-xs bg-transparent">
          Clear all filters
        </Button>
      )}
    </div>
  )
}
