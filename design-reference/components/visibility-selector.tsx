"use client"
import { Lock, Globe, Users } from "lucide-react"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import type { PromptVisibility } from "@/types"
import { VISIBILITY_LABELS } from "@/lib/constants"

interface VisibilitySelectorProps {
  value: PromptVisibility
  onChange: (visibility: PromptVisibility) => void
  disabled?: boolean
}

export function VisibilitySelector({ value, onChange, disabled }: VisibilitySelectorProps) {
  const visibilityOptions = [
    {
      value: "private" as const,
      label: VISIBILITY_LABELS.private,
      description: "Only you can see this prompt",
      icon: Lock,
    },
    {
      value: "shared" as const,
      label: VISIBILITY_LABELS.shared,
      description: "Only people you share with can see this",
      icon: Users,
    },
    {
      value: "public" as const,
      label: VISIBILITY_LABELS.public,
      description: "Anyone can discover and view this prompt",
      icon: Globe,
    },
  ]

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium">Visibility</Label>
      <RadioGroup value={value} onValueChange={onChange} disabled={disabled}>
        {visibilityOptions.map((option) => (
          <div key={option.value} className="flex items-start space-x-3">
            <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
            <div className="flex-1 space-y-1">
              <Label htmlFor={option.value} className="flex items-center gap-2 cursor-pointer">
                <option.icon className="h-4 w-4" />
                {option.label}
              </Label>
              <p className="text-xs text-muted-foreground">{option.description}</p>
            </div>
          </div>
        ))}
      </RadioGroup>
    </div>
  )
}
