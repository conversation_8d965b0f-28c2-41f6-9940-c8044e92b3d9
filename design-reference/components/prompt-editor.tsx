"use client"
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { Prompt } from "@/types"
import { TARGET_AI_OPTIONS } from "@/lib/constants"
import { usePromptEditor } from "@/hooks/use-prompt-editor"

interface PromptEditorProps {
  prompt?: Prompt
  onSave?: () => void
  onCancel?: () => void
  onBack?: () => void
}

export function PromptEditor({ prompt, onSave, onCancel, onBack }: PromptEditorProps) {
  const { draft, updateDraft, createPromptDraft, editPromptDraft, isLoading } = usePromptEditor(
    prompt
      ? {
          title: prompt.title,
          content: prompt.content,
          targetAI: prompt.targetAI,
        }
      : undefined,
  )

  const handleSave = async () => {
    if (prompt) {
      await editPromptDraft(prompt.id, draft)
    } else {
      await createPromptDraft(draft)
    }
    onSave?.()
  }

  const isValid = draft.title.trim() && draft.content.trim()

  return (
    <Card className="w-full lg:max-w-4xl lg:mx-auto">
      <CardHeader className="lg:pb-8">
        <div className="flex items-center gap-3 lg:gap-4">
          <Button variant="ghost" size="icon" onClick={onBack || onCancel} className="h-8 w-8 lg:h-10 lg:w-10">
            <ArrowLeft className="h-4 w-4 lg:h-5 lg:w-5" />
          </Button>
          <CardTitle className="sm:text-xl lg:text-2xl">Back {prompt ? "Edit Prompt" : "Create New Prompt"}</CardTitle>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 sm:space-y-6 lg:space-y-8">
        <div className="space-y-2 lg:space-y-3">
          <Label htmlFor="title" className="sm:text-base lg:text-lg">
            Title
          </Label>
          <Input
            id="title"
            placeholder="Enter prompt title..."
            value={draft.title}
            onChange={(e) => updateDraft({ title: e.target.value })}
            className="sm:h-11 lg:h-12 lg:text-lg"
          />
        </div>

        <div className="space-y-2 lg:space-y-3">
          <Label htmlFor="content" className="sm:text-base lg:text-lg">
            Content
          </Label>
          <Textarea
            id="content"
            placeholder="Enter your prompt content..."
            value={draft.content}
            onChange={(e) => updateDraft({ content: e.target.value })}
            rows={8}
            className="resize-none sm:rows-12 sm:text-base lg:min-h-[300px] lg:text-lg lg:leading-relaxed"
          />
        </div>

        <div className="space-y-2 lg:space-y-3">
          <Label htmlFor="targetAI" className="sm:text-base lg:text-lg">
            Target AI
          </Label>
          <Select value={draft.targetAI} onValueChange={(value) => updateDraft({ targetAI: value as any })}>
            <SelectTrigger className="h-auto min-h-[2.5rem] sm:min-h-[3rem] lg:min-h-[3.5rem]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {TARGET_AI_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value} className="py-3 sm:py-4 lg:py-5">
                  <div>
                    <div className="font-medium sm:text-base lg:text-lg">{option.label}</div>
                    <div className="text-xs text-muted-foreground sm:text-sm lg:text-base">{option.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>

      <CardFooter className="flex gap-2 sm:gap-4 lg:gap-6 lg:pt-8">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
          className="flex-1 bg-transparent sm:h-11 lg:h-12 lg:text-lg"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          disabled={!isValid || isLoading}
          className="flex-1 sm:h-11 lg:h-12 lg:text-lg order-last"
        >
          {isLoading ? "Saving..." : "Save"}
        </Button>
      </CardFooter>
    </Card>
  )
}
