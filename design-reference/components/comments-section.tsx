"use client"
import { MessageCircle } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { CommentForm } from "./comment-form"
import { CommentItem } from "./comment-item"
import type { Comment } from "@/types"

interface CommentsSectionProps {
  promptId: string
  comments: Comment[]
  onCommentsUpdated?: () => void
}

export function CommentsSection({ promptId, comments, onCommentsUpdated }: CommentsSectionProps) {
  const promptComments = comments.filter((comment) => comment.promptId === promptId)

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <MessageCircle className="h-4 w-4" />
          Comments ({promptComments.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <CommentForm promptId={promptId} onCommentAdded={onCommentsUpdated} />

        {promptComments.length > 0 && (
          <div className="space-y-4 pt-2 border-t">
            {promptComments.map((comment) => (
              <CommentItem key={comment.id} comment={comment} onCommentUpdated={onCommentsUpdated} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
