"use client"
import { useState } from "react"
import { Check, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { mockUsers, currentUser } from "@/lib/mock-data"

interface UserSelectorProps {
  selectedUserIds: string[]
  onChange: (userIds: string[]) => void
  disabled?: boolean
}

export function UserSelector({ selectedUserIds, onChange, disabled }: UserSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // Filter out current user and search by name/email
  const availableUsers = mockUsers.filter(
    (user) =>
      user.id !== currentUser.id &&
      (user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase())),
  )

  const selectedUsers = mockUsers.filter((user) => selectedUserIds.includes(user.id))

  const handleUserToggle = (userId: string) => {
    if (selectedUserIds.includes(userId)) {
      onChange(selectedUserIds.filter((id) => id !== userId))
    } else {
      onChange([...selectedUserIds, userId])
    }
  }

  const handleRemoveUser = (userId: string) => {
    onChange(selectedUserIds.filter((id) => id !== userId))
  }

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium">Share with users</Label>

      {/* Selected users */}
      {selectedUsers.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedUsers.map((user) => (
            <Badge key={user.id} variant="secondary" className="flex items-center gap-1">
              <div className="h-4 w-4 rounded-full bg-primary/20 flex items-center justify-center text-xs font-bold">
                {user.name.charAt(0)}
              </div>
              <span className="text-xs">{user.name}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-3 w-3 p-0 hover:bg-transparent"
                onClick={() => handleRemoveUser(user.id)}
                disabled={disabled}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* Search input */}
      <Input
        placeholder="Search users by name or email..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        disabled={disabled}
      />

      {/* Available users */}
      {searchQuery && (
        <div className="max-h-40 overflow-y-auto space-y-1 border rounded-md p-2">
          {availableUsers.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-2">No users found</p>
          ) : (
            availableUsers.map((user) => (
              <div
                key={user.id}
                className="flex items-center justify-between p-2 hover:bg-accent rounded-sm cursor-pointer"
                onClick={() => handleUserToggle(user.id)}
              >
                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-bold">
                    {user.name.charAt(0)}
                  </div>
                  <div>
                    <p className="text-sm font-medium">{user.name}</p>
                    <p className="text-xs text-muted-foreground">{user.email}</p>
                  </div>
                </div>
                {selectedUserIds.includes(user.id) && <Check className="h-4 w-4 text-primary" />}
              </div>
            ))
          )}
        </div>
      )}
    </div>
  )
}
