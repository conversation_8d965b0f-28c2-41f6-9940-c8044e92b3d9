"use client"
import { Filter, Heart, Archive, Users, Tag } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { PromptFilters, TargetAI, PromptStatus } from "@/types"
import { TARGET_AI_OPTIONS, PROMPT_STATUS_LABELS } from "@/lib/constants"
import { mockTags, mockUsers } from "@/lib/mock-data"

interface FilterMenuProps {
  filters: PromptFilters
  onFilterChange: (filters: Partial<PromptFilters>) => void
}

export function FilterMenu({ filters, onFilterChange }: FilterMenuProps) {
  const handleStatusFilter = (status: PromptStatus) => {
    onFilterChange({ status: filters.status === status ? undefined : status })
  }

  const handleTargetAIFilter = (targetAI: TargetAI) => {
    onFilterChange({ targetAI: filters.targetAI === targetAI ? undefined : targetAI })
  }

  const handleAuthorFilter = (authorId: string) => {
    onFilterChange({ authorId: filters.authorId === authorId ? undefined : authorId })
  }

  const handleTagFilter = (tagId: string) => {
    const currentTags = filters.tags || []
    const newTags = currentTags.includes(tagId) ? currentTags.filter((id) => id !== tagId) : [...currentTags, tagId]
    onFilterChange({ tags: newTags.length > 0 ? newTags : undefined })
  }

  const handleFavoritesFilter = () => {
    onFilterChange({ isFavorite: !filters.isFavorite })
  }

  const handleSharedFilter = () => {
    onFilterChange({ sharedWithMe: !filters.sharedWithMe })
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="h-9 w-9 bg-transparent">
          <Filter className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Quick Filters</DropdownMenuLabel>
        <DropdownMenuItem onClick={handleFavoritesFilter}>
          <Heart className={`h-4 w-4 mr-2 ${filters.isFavorite ? "fill-red-500 text-red-500" : ""}`} />
          Favorites
          {filters.isFavorite && (
            <Badge variant="secondary" className="ml-auto text-xs">
              Active
            </Badge>
          )}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleSharedFilter}>
          <Users className="h-4 w-4 mr-2" />
          Shared with me
          {filters.sharedWithMe && (
            <Badge variant="secondary" className="ml-auto text-xs">
              Active
            </Badge>
          )}
        </DropdownMenuItem>

        <DropdownMenuSeparator />
        <DropdownMenuLabel>Status</DropdownMenuLabel>
        {Object.entries(PROMPT_STATUS_LABELS).map(([status, label]) => (
          <DropdownMenuItem key={status} onClick={() => handleStatusFilter(status as PromptStatus)}>
            <Archive className="h-4 w-4 mr-2" />
            {label}
            {filters.status === status && (
              <Badge variant="secondary" className="ml-auto text-xs">
                Active
              </Badge>
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />
        <DropdownMenuLabel>Target AI</DropdownMenuLabel>
        {TARGET_AI_OPTIONS.map((ai) => (
          <DropdownMenuItem key={ai.value} onClick={() => handleTargetAIFilter(ai.value)}>
            <span className="h-4 w-4 mr-2 flex items-center justify-center text-xs font-bold bg-primary/10 rounded">
              {ai.label.charAt(0)}
            </span>
            {ai.label}
            {filters.targetAI === ai.value && (
              <Badge variant="secondary" className="ml-auto text-xs">
                Active
              </Badge>
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />
        <DropdownMenuLabel>Authors</DropdownMenuLabel>
        {mockUsers.map((user) => (
          <DropdownMenuItem key={user.id} onClick={() => handleAuthorFilter(user.id)}>
            <div className="h-4 w-4 mr-2 rounded-full bg-primary/10 flex items-center justify-center text-xs font-bold">
              {user.name.charAt(0)}
            </div>
            {user.name}
            {filters.authorId === user.id && (
              <Badge variant="secondary" className="ml-auto text-xs">
                Active
              </Badge>
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />
        <DropdownMenuLabel>Tags</DropdownMenuLabel>
        {mockTags.map((tag) => (
          <DropdownMenuItem key={tag.id} onClick={() => handleTagFilter(tag.id)}>
            <Tag className="h-4 w-4 mr-2" />
            {tag.name}
            {filters.tags?.includes(tag.id) && (
              <Badge variant="secondary" className="ml-auto text-xs">
                Active
              </Badge>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
