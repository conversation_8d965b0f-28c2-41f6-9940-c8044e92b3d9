"use client"
import { <PERSON>, Archive, Trash2, <PERSON>, MoreVertical, Eye, <PERSON>, <PERSON>hare2, <PERSON><PERSON>, Check } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { SharingDialog } from "./sharing-dialog"
import type { Prompt } from "@/types"
import { TARGET_AI_OPTIONS, PROMPT_STATUS_LABELS } from "@/lib/constants"
import { usePromptActions } from "@/hooks/use-prompt-actions"
import { useClipboard } from "@/hooks/use-clipboard"

interface PromptCardProps {
  prompt: Prompt
  onEdit?: (prompt: Prompt) => void
  onView?: (prompt: Prompt) => void
  onPromptUpdated?: () => void
}

export function PromptCard({ prompt, onEdit, onView, onPromptUpdated }: PromptCardProps) {
  const {
    addPromptToFavorites,
    removePromptFromFavorites,
    archivePrompt,
    restorePrompt,
    deletePrompt,
    duplicatePrompt,
    publishPrompt,
    isLoading,
  } = usePromptActions()

  const { copyToClipboard, isCopied } = useClipboard()

  const targetAILabel = TARGET_AI_OPTIONS.find((ai) => ai.value === prompt.targetAI)?.label || prompt.targetAI
  const statusLabel = PROMPT_STATUS_LABELS[prompt.status]

  const handleFavoriteToggle = () => {
    if (prompt.isFavorite) {
      removePromptFromFavorites(prompt.id)
    } else {
      addPromptToFavorites(prompt.id)
    }
  }

  const handleArchiveToggle = () => {
    if (prompt.status === "archived") {
      restorePrompt(prompt.id)
    } else {
      archivePrompt(prompt.id)
    }
  }

  const handleCopy = () => {
    copyToClipboard(prompt.content)
  }

  const handleTitleClick = () => {
    onView?.(prompt)
  }

  const handleContentClick = () => {
    onView?.(prompt)
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-0">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            <h3
              className="font-semibold leading-tight truncate cursor-pointer hover:text-primary transition-colors text-lg"
              onClick={handleTitleClick}
            >
              {prompt.title}
            </h3>
            <p className="text-xs text-muted-foreground mt-1">by {prompt.author.name}</p>
          </div>
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handleFavoriteToggle} disabled={isLoading}>
              <Heart className={`h-3.5 w-3.5 ${prompt.isFavorite ? "fill-red-500 text-red-500" : ""}`} />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-7 w-7">
                  <MoreVertical className="h-3.5 w-3.5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onView?.(prompt)}>
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onEdit?.(prompt)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => duplicatePrompt(prompt.id)}>
                  <Check className="h-4 w-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                {prompt.status === "draft" && (
                  <DropdownMenuItem onClick={() => publishPrompt(prompt.id)}>
                    <Users className="h-4 w-4 mr-2" />
                    Publish
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleArchiveToggle}>
                  <Archive className="h-4 w-4 mr-2" />
                  {prompt.status === "archived" ? "Restore" : "Archive"}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => deletePrompt(prompt.id)} className="text-destructive">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent className="py-0">
        <p
          className="text-sm leading-relaxed cursor-pointer hover:text-indigo-100 transition-colors line-clamp-3 text-neutral-200"
          onClick={handleContentClick}
        >
          {prompt.content}
        </p>
      </CardContent>

      <CardFooter className="flex-col items-start gap-2 pt-0">
        <div className="flex flex-wrap gap-1">
          {prompt.tags.map((tag) => (
            <Badge key={tag.id} variant="secondary" className="text-xs px-2 py-0.5">
              {tag.name}
            </Badge>
          ))}
        </div>

        <div className="flex items-center justify-between w-full my-2">
          <div className="flex items-center gap-3 text-xs text-muted-foreground">
            <span>{targetAILabel}</span>
            <Badge variant={prompt.status === "published" ? "default" : "outline"} className="text-xs">
              {statusLabel}
            </Badge>
            {prompt.visibility === "shared" && prompt.sharedWith.length > 0 && (
              <Badge variant="outline" className="text-xs">
                Shared with {prompt.sharedWith.length}
              </Badge>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2 w-full">
          <SharingDialog prompt={prompt} onPromptUpdated={onPromptUpdated}>
            <Button variant="default" size="sm" className="flex-1 h-8 text-xs">
              <Share2 className="h-3.5 w-3.5 mr-1.5" />
              Share
            </Button>
          </SharingDialog>

          <Button
            variant="default"
            size="sm"
            onClick={handleCopy}
            className="flex-1 h-8 text-xs bg-secondary"
          >
            {isCopied ? (
              <Check className="h-3.5 w-3.5 mr-1.5 text-white" />
            ) : (
              <Copy className="h-3.5 w-3.5 mr-1.5 text-white" />
            )}
            {isCopied ? "Copied!" : "Copy"}
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
