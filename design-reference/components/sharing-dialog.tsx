"use client"
import { Share } from "lucide-react"
import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { VisibilitySelector } from "./visibility-selector"
import { UserSelector } from "./user-selector"
import { ShareLinkSection } from "./share-link-section"
import { AccessList } from "./access-list"
import { useSharingActions } from "@/hooks/use-sharing-actions"
import type { Prompt, PromptVisibility } from "@/types"
import { useState } from "react"

interface SharingDialogProps {
  prompt: Prompt
  onPromptUpdated?: () => void
  children?: React.ReactNode
}

export function SharingDialog({ prompt, onPromptUpdated, children }: SharingDialogProps) {
  const [open, setOpen] = useState(false)
  const [visibility, setVisibility] = useState<PromptVisibility>(prompt.visibility)
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>(prompt.sharedWith)
  const { setPromptVisibility, sharePromptWithUsers, isLoading } = useSharingActions()

  const handleSave = async () => {
    // Update visibility if changed
    if (visibility !== prompt.visibility) {
      await setPromptVisibility(prompt.id, visibility)
    }

    // Update shared users if changed
    const currentSharedUsers = new Set(prompt.sharedWith)
    const newSharedUsers = new Set(selectedUserIds)
    const hasChanges =
      currentSharedUsers.size !== newSharedUsers.size || [...currentSharedUsers].some((id) => !newSharedUsers.has(id))

    if (hasChanges) {
      await sharePromptWithUsers(prompt.id, selectedUserIds)
    }

    onPromptUpdated?.()
    setOpen(false)
  }

  const handleCancel = () => {
    setVisibility(prompt.visibility)
    setSelectedUserIds(prompt.sharedWith)
    setOpen(false)
  }

  const handleUserRemoved = () => {
    onPromptUpdated?.()
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="secondary" size="sm" className="bg-transparent">
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Share "{prompt.title}"</DialogTitle>
          <DialogDescription>Control who can see and access this prompt.</DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <VisibilitySelector value={visibility} onChange={setVisibility} disabled={isLoading} />

          {visibility === "shared" && (
            <>
              <UserSelector selectedUserIds={selectedUserIds} onChange={setSelectedUserIds} disabled={isLoading} />

              <ShareLinkSection promptId={prompt.id} currentShareLink={prompt.shareLink} disabled={isLoading} />

              <AccessList
                promptId={prompt.id}
                sharedWithUserIds={prompt.sharedWith}
                onUserRemoved={handleUserRemoved}
                disabled={isLoading}
              />
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={isLoading} className="bg-transparent">
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
