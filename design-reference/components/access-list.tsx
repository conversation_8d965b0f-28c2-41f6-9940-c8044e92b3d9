"use client"
import { Users, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { mockUsers } from "@/lib/mock-data"
import { useSharingActions } from "@/hooks/use-sharing-actions"

interface AccessListProps {
  promptId: string
  sharedWithUserIds: string[]
  onUserRemoved?: () => void
  disabled?: boolean
}

export function AccessList({ promptId, sharedWithUserIds, onUserRemoved, disabled }: AccessListProps) {
  const { removeUserAccessFromPrompt, viewPromptAccessList, isLoading } = useSharingActions()

  const sharedUsers = mockUsers.filter((user) => sharedWithUserIds.includes(user.id))

  const handleRemoveAccess = async (userId: string) => {
    await removeUserAccessFromPrompt(promptId, userId)
    onUserRemoved?.()
  }

  const handleViewAccessList = () => {
    viewPromptAccessList(promptId)
  }

  if (sharedUsers.length === 0) {
    return null
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium flex items-center gap-2">
          <Users className="h-4 w-4" />
          People with access ({sharedUsers.length})
        </Label>
        <Button variant="ghost" size="sm" onClick={handleViewAccessList} className="text-xs h-6">
          View all
        </Button>
      </div>

      <div className="space-y-2">
        {sharedUsers.map((user) => (
          <div key={user.id} className="flex items-center justify-between p-2 border rounded-md">
            <div className="flex items-center gap-2">
              <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-bold">
                {user.name.charAt(0)}
              </div>
              <div>
                <p className="text-sm font-medium">{user.name}</p>
                <p className="text-xs text-muted-foreground">{user.email}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => handleRemoveAccess(user.id)}
              disabled={disabled || isLoading}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}
