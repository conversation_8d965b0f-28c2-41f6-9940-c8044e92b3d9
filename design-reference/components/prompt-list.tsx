"use client"
import type { Prompt } from "@/types"
import { PromptCard } from "./prompt-card"

interface PromptListProps {
  prompts: Prompt[]
  onEditPrompt?: (prompt: Prompt) => void
  onViewPrompt?: (prompt: Prompt) => void
  onPromptUpdated?: () => void
  emptyMessage?: string
}

export function PromptList({
  prompts,
  onEditPrompt,
  onViewPrompt,
  onPromptUpdated,
  emptyMessage = "No prompts found",
}: PromptListProps) {
  if (prompts.length === 0) {
    return (
      <div className="text-center py-12 lg:py-16">
        <p className="text-muted-foreground lg:text-lg">{emptyMessage}</p>
      </div>
    )
  }

  return (
    <div className="space-y-4 sm:grid sm:grid-cols-2 sm:gap-4 sm:space-y-0 md:gap-6 lg:grid-cols-3 lg:gap-8">
      {prompts.map((prompt) => (
        <PromptCard key={prompt.id} prompt={prompt} onEdit={onEditPrompt} onView={onViewPrompt} />
      ))}
    </div>
  )
}
