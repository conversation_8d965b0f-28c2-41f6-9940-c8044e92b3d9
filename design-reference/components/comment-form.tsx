"use client"
import { useState } from "react"
import type React from "react"

import { Send } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useCollaborationActions } from "@/hooks/use-collaboration-actions"

interface CommentFormProps {
  promptId: string
  onCommentAdded?: () => void
}

export function CommentForm({ promptId, onCommentAdded }: CommentFormProps) {
  const [content, setContent] = useState("")
  const { addCommentToPrompt, isLoading } = useCollaborationActions()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!content.trim()) return

    await addCommentToPrompt(promptId, content.trim())
    setContent("")
    onCommentAdded?.()
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <Textarea
        placeholder="Add a comment..."
        value={content}
        onChange={(e) => setContent(e.target.value)}
        rows={3}
        className="resize-none"
      />
      <div className="flex justify-end">
        <Button type="submit" size="sm" disabled={!content.trim() || isLoading}>
          <Send className="h-4 w-4 mr-2" />
          {isLoading ? "Posting..." : "Post Comment"}
        </Button>
      </div>
    </form>
  )
}
