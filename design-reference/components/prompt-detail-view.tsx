"use client"
import { <PERSON><PERSON><PERSON><PERSON>, Heart, Archive, Co<PERSON>, Trash2, <PERSON>, MoreVertical, Share2, Check } from "lucide-react"
import React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { CommentsSection } from "./comments-section"
import { SharingDialog } from "./sharing-dialog"
import { PromptPersonalization } from "./prompt-personalization"
import type { Prompt, Comment } from "@/types"
import { TARGET_AI_OPTIONS, PROMPT_STATUS_LABELS, VISIBILITY_LABELS } from "@/lib/constants"
import { usePromptActions } from "@/hooks/use-prompt-actions"
import { useCollaborationActions } from "@/hooks/use-collaboration-actions"
import { useClipboard } from "@/hooks/use-clipboard"

interface PromptDetailViewProps {
  prompt: Prompt
  comments: Comment[]
  onBack: () => void
  onEdit?: (prompt: Prompt) => void
  onCommentsUpdated?: () => void
  onPromptUpdated?: () => void
}

export function PromptDetailView({
  prompt,
  comments,
  onBack,
  onEdit,
  onCommentsUpdated,
  onPromptUpdated,
}: PromptDetailViewProps) {
  const {
    addPromptToFavorites,
    removePromptFromFavorites,
    archivePrompt,
    restorePrompt,
    deletePrompt,
    duplicatePrompt,
    publishPrompt,
    isLoading: actionsLoading,
  } = usePromptActions()

  const { receivePromptCommentsInRealtime } = useCollaborationActions()
  const { copyToClipboard, isCopied } = useClipboard()

  const targetAILabel = TARGET_AI_OPTIONS.find((ai) => ai.value === prompt.targetAI)?.label || prompt.targetAI
  const statusLabel = PROMPT_STATUS_LABELS[prompt.status]
  const visibilityLabel = VISIBILITY_LABELS[prompt.visibility]

  const handleFavoriteToggle = () => {
    if (prompt.isFavorite) {
      removePromptFromFavorites(prompt.id)
    } else {
      addPromptToFavorites(prompt.id)
    }
  }

  const handleArchiveToggle = () => {
    if (prompt.status === "archived") {
      restorePrompt(prompt.id)
    } else {
      archivePrompt(prompt.id)
    }
  }

  const handleCopy = () => {
    copyToClipboard(prompt.content)
  }

  React.useEffect(() => {
    receivePromptCommentsInRealtime(prompt.id)
  }, [prompt.id, receivePromptCommentsInRealtime])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack} className="h-9 px-3 gap-2">
          <ArrowLeft className="h-4 w-4" />
          <span className="text-sm">Back</span>
        </Button>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9"
            onClick={handleFavoriteToggle}
            disabled={actionsLoading}
          >
            <Heart className={`h-4 w-4 ${prompt.isFavorite ? "fill-red-500 text-red-500" : ""}`} />
          </Button>
          <SharingDialog prompt={prompt} onPromptUpdated={onPromptUpdated}>
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <Share2 className="h-4 w-4" />
            </Button>
          </SharingDialog>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit?.(prompt)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => duplicatePrompt(prompt.id)}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </DropdownMenuItem>
              {prompt.status === "draft" && (
                <DropdownMenuItem onClick={() => publishPrompt(prompt.id)}>
                  <Archive className="h-4 w-4 mr-2" />
                  Publish
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleArchiveToggle}>
                <Archive className="h-4 w-4 mr-2" />
                {prompt.status === "archived" ? "Restore" : "Archive"}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => deletePrompt(prompt.id)} className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Prompt Content */}
      <Card>
        <CardHeader className="pb-3">
          <div className="space-y-2">
            <h1 className="text-xl font-bold">{prompt.title}</h1>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>by {prompt.author.name}</span>
              <span>•</span>
              <span>{new Date(prompt.updatedAt).toLocaleDateString()}</span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="prose prose-sm max-w-none">
            <p className="whitespace-pre-wrap">{prompt.content}</p>
          </div>

          <div className="flex flex-wrap gap-2">
            {prompt.tags.map((tag) => (
              <Badge key={tag.id} variant="secondary">
                {tag.name}
              </Badge>
            ))}
          </div>

          <div className="flex items-center justify-between pt-2 border-t text-sm text-muted-foreground">
            <div className="flex items-center gap-3">
              <span>{targetAILabel}</span>
              <Badge variant={prompt.status === "published" ? "default" : "outline"}>{statusLabel}</Badge>
              <Badge variant="outline">{visibilityLabel}</Badge>
              {prompt.visibility === "shared" && prompt.sharedWith.length > 0 && (
                <Badge variant="outline">Shared with {prompt.sharedWith.length}</Badge>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2 w-full pt-4">
            <SharingDialog prompt={prompt} onPromptUpdated={onPromptUpdated}>
              <Button variant="default" size="sm" className="flex-1 h-8 text-xs">
                <Share2 className="h-3.5 w-3.5 mr-1.5" />
                Share
              </Button>
            </SharingDialog>

            <Button
              variant="default"
              size="sm"
              onClick={handleCopy}
              className="flex-1 h-8 text-xs text-white bg-secondary"
            >
              {isCopied ? (
                <Check className="h-3.5 w-3.5 mr-1.5 text-white" />
              ) : (
                <Copy className="h-3.5 w-3.5 mr-1.5 text-white" />
              )}
              {isCopied ? "Copied!" : "Copy"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Prompt Personalization */}
      <PromptPersonalization content={prompt.content} />

      {/* Comments */}
      <CommentsSection promptId={prompt.id} comments={comments} onCommentsUpdated={onCommentsUpdated} />
    </div>
  )
}
