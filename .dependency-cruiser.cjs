module.exports = {
    forbidden: [
        {
            name: 'no-cross-boundary-client-server',
            severity: 'error',
            comment: 'Client modules should not directly depend on server modules and vice versa.',
            from: {
                path: '^client',
            },
            to: {
                path: '^server',
            },
        },
        {
            name: 'no-infrastructure-in-domain',
            severity: 'error',
            comment: 'Domain modules should not directly depend on infrastructure modules.',
            from: {
                path: '^(client|server)/.*/domain',
            },
            to: {
                path: '^(client|server)/.*/infrastructure',
            },
        },
        {
            name: 'no-application-in-domain',
            severity: 'error',
            comment: 'Domain modules should not directly depend on application modules.',
            from: {
                path: '^(client|server)/.*/domain',
            },
            to: {
                path: '^(client|server)/.*/application',
            },
        },
        {
            name: 'no-presentation-in-domain',
            severity: 'error',
            comment: 'Domain modules should not directly depend on presentation modules.',
            from: {
                path: '^(client|server)/.*/domain',
            },
            to: {
                path: '^(client|server)/.*/presentation',
            },
        },
        {
            name: 'no-infrastructure-in-application',
            severity: 'error',
            comment: 'Application modules should not directly depend on infrastructure modules.',
            from: {
                path: '^.*/application',
            },
            to: {
                path: '^.*/infrastructure',
            },
        },
    ],
    options: {
        tsPreCompilationDeps: true,
        exclude: '(node_modules|dist|coverage|\\.spec\\.ts$|\\.test\\.ts$)',
        tsConfig: {
            fileName: './tsconfig.json'
        },
        reporterOptions: {
            dot: {
                collapsePattern: 'node_modules/(?:@[^/]+/[^/]+|[^/]+)',
                theme: {
                    modules: [
                        {
                            criteria: {source: '^src/client/.*/domain'},
                            attributes: {
                                fillcolor: '#fa9f36',
                                fontcolor: 'black',
                                style: 'filled',
                            },
                        },
                        {
                            criteria: {source: '^src/client/.*/application'},
                            attributes: {
                                fillcolor: '#fb6969',
                                fontcolor: 'black',
                                style: 'filled',
                            },
                        },
                        {
                            criteria: {source: '^src/client/.*/infrastructure'},
                            attributes: {
                                fillcolor: '#6cbaff',
                                fontcolor: 'black',
                                style: 'filled',
                            },
                        },
                        {
                            criteria: {source: '^src/client/.*/presentation'},
                            attributes: {
                                fillcolor: '#9fff9f',
                                fontcolor: 'black',
                                style: 'filled',
                            },
                        },
                        {
                            criteria: {source: '^src/server/.*/domain'},
                            attributes: {
                                fillcolor: '#dc6b0d',
                                fontcolor: 'white',
                                style: 'filled',
                            },
                        },
                        {
                            criteria: {source: '^src/server/.*/application'},
                            attributes: {
                                fillcolor: '#dd1c1c',
                                fontcolor: 'white',
                                style: 'filled',
                            },
                        },
                        {
                            criteria: {source: '^src/server/.*/infrastructure'},
                            attributes: {
                                fillcolor: '#248cea',
                                fontcolor: 'white',
                                style: 'filled',
                            },
                        },
                        {
                            criteria: {source: '^src/server/.*/presentation'},
                            attributes: {
                                fillcolor: '#20b520',
                                fontcolor: 'white',
                                style: 'filled',
                            },
                        },
                    ],
                },
            },
        },
    },
};
